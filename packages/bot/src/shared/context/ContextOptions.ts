/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ApplicationCommandOptionType,
  ChatInputCommandInteraction,
  ContextMenuCommandInteraction,
  Message,
  MessageComponentInteraction,
  ModalSubmitInteraction,
  User,
  Channel,
  Role,
  Attachment,
} from 'discord.js';
import type { Context, ValidContextInteractions } from './Context.js';

/**
 * Custom error class for option retrieval errors
 */
export class OptionError extends Error {
  constructor(
    message: string,
    public readonly optionName: string,
  ) {
    super(message);
    this.name = 'OptionError';
  }
}

/**
 * Context options handler for retrieving command options from interactions
 */
export class ContextOptions {
  constructor(private readonly context: Context) {}

  /**
   * Get a string option
   */
  getString(name: string, required?: boolean): string | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getString(name, required);
      return value;
    }

    // For prefix commands, we'll need to implement argument parsing
    // This is a simplified version - the full implementation would be more complex
    return null;
  }

  /**
   * Get an integer option
   */
  getInteger(name: string, required?: boolean): number | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getInteger(name, required);
      return value;
    }

    return null;
  }

  /**
   * Get a number option
   */
  getNumber(name: string, required?: boolean): number | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getNumber(name, required);
      return value;
    }

    return null;
  }

  /**
   * Get a boolean option
   */
  getBoolean(name: string, required?: boolean): boolean | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getBoolean(name, required);
      return value;
    }

    return null;
  }

  /**
   * Get a user option
   */
  async getUser(name: string, required?: boolean): Promise<User | null> {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getUser(name, required);
      return value;
    }

    return null;
  }

  /**
   * Get a member option
   */
  getMember(name: string) {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getMember(name);
      return value;
    }

    return null;
  }

  /**
   * Get a channel option
   */
  getChannel(name: string, required?: boolean) {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getChannel(name, required);
      return value;
    }

    return null;
  }

  /**
   * Get a role option
   */
  getRole(name: string, required?: boolean) {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getRole(name, required);
      return value;
    }

    return null;
  }

  /**
   * Get an attachment option
   */
  getAttachment(name: string, required?: boolean): Attachment | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getAttachment(name, required);
      return value;
    }

    return null;
  }

  /**
   * Get a mentionable option
   */
  getMentionable(name: string, required?: boolean) {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getMentionable(name, required);
      return value;
    }

    return null;
  }

  /**
   * Get the subcommand name
   */
  getSubcommand(required?: boolean): string | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      try {
        return interaction.options.getSubcommand(required ?? false);
      } catch {
        return null;
      }
    }

    return null;
  }

  /**
   * Get the subcommand group name
   */
  getSubcommandGroup(required?: boolean): string | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      try {
        return interaction.options.getSubcommandGroup(required ?? false);
      } catch {
        return null;
      }
    }

    return null;
  }
}
