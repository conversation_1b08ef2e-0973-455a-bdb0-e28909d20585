/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Base Domain Error Class
 *
 * All domain-specific errors should extend from this base class.
 * This provides a consistent error structure throughout the application.
 */
export abstract class DomainError extends Error {
  abstract readonly code: string;
  abstract readonly statusCode: number;

  constructor(
    message: string,
    public readonly context?: Record<string, any>,
    public readonly cause?: Error
  ) {
    super(message);
    this.name = this.constructor.name;

    // Maintains proper stack trace for where our error was thrown
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Returns a serializable representation of the error
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      context: this.context,
      stack: this.stack,
    };
  }
}

/**
 * Application-level errors (use cases, services)
 */
export abstract class ApplicationError extends DomainError {}

/**
 * Infrastructure-level errors (database, external APIs)
 */
export abstract class InfrastructureError extends DomainError {}

/**
 * Validation errors
 */
export class ValidationError extends DomainError {
  readonly code = 'VALIDATION_ERROR';
  readonly statusCode = 400;

  constructor(
    message: string,
    public readonly validationErrors: Record<string, string[]>,
    context?: Record<string, any>
  ) {
    super(message, { ...context, validationErrors });
  }
}

/**
 * Not found errors
 */
export class NotFoundError extends DomainError {
  readonly code = 'NOT_FOUND';
  readonly statusCode = 404;

  constructor(resource: string, id: string, context?: Record<string, any>) {
    super(`${resource} with ID ${id} not found`, { ...context, resource, id });
  }
}

/**
 * Permission denied errors
 */
export class PermissionDeniedError extends DomainError {
  readonly code = 'PERMISSION_DENIED';
  readonly statusCode = 403;

  constructor(action: string, resource?: string, context?: Record<string, any>) {
    const message = resource
      ? `Permission denied: cannot ${action} ${resource}`
      : `Permission denied: cannot ${action}`;

    super(message, { ...context, action, resource });
  }
}

/**
 * Business rule violation errors
 */
export class BusinessRuleViolationError extends DomainError {
  readonly code = 'BUSINESS_RULE_VIOLATION';
  readonly statusCode = 422;

  constructor(rule: string, context?: Record<string, any>) {
    super(`Business rule violation: ${rule}`, context);
  }
}

/**
 * External service errors
 */
export class ExternalServiceError extends InfrastructureError {
  readonly code = 'EXTERNAL_SERVICE_ERROR';
  readonly statusCode = 503;

  constructor(service: string, message: string, context?: Record<string, any>) {
    super(`External service error (${service}): ${message}`, { ...context, service });
  }
}

/**
 * Database errors
 */
export class DatabaseError extends InfrastructureError {
  readonly code = 'DATABASE_ERROR';
  readonly statusCode = 500;

  constructor(operation: string, message: string, context?: Record<string, any>) {
    super(`Database error during ${operation}: ${message}`, { ...context, operation });
  }
}
