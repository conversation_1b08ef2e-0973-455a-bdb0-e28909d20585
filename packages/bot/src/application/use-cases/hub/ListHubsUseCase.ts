/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import { Hub } from '../../../domain/entities/Hub.js';
import type { IHubRepository } from '../../../domain/repositories/HubRepositories.js';
import { TYPES } from '../../../shared/types/TYPES.js';

export interface ListHubsRequest {
  userId?: string; // Filter hubs by user ownership
  searchPattern?: string; // Search by name pattern
  includePrivate?: boolean; // Include private hubs (only for owned/moderated)
  onlyPopular?: boolean; // Show only popular public hubs
  limit?: number; // Maximum number of results
  skip?: number; // For pagination
}

export interface ListHubsResponse {
  success: boolean;
  hubs?: Hub[];
  total?: number;
  error?: string;
}

@injectable()
export class ListHubsUseCase {
  constructor(@inject(TYPES.HubRepository) private hubRepository: IHubRepository) {}

  async execute(request: ListHubsRequest): Promise<ListHubsResponse> {
    try {
      let hubs: Hub[] = [];

      if (request.onlyPopular) {
        // Get popular public hubs
        hubs = await this.hubRepository.findPopular(request.limit || 10);
      } else if (request.userId) {
        // Get hubs owned by the user
        hubs = await this.hubRepository.findByOwnerId(request.userId);

        // If includePrivate is false, filter out private hubs
        if (!request.includePrivate) {
          hubs = hubs.filter(hub => !hub.isPrivate);
        }
      } else if (request.searchPattern) {
        // Search hubs by name pattern
        const options = {
          searchType: 'contains' as const,
          isPrivate: request.includePrivate ? undefined : false,
          take: request.limit || 20,
        };

        hubs = await this.hubRepository.findByNamePattern(request.searchPattern, options);
      } else {
        // Get hubs with filters
        const filters = {
          isPrivate: request.includePrivate ? undefined : false,
          skip: request.skip || 0,
          take: request.limit || 20,
        };

        hubs = await this.hubRepository.findWithFilters(filters);
      }

      // Apply additional filtering and pagination if needed
      if (request.skip && !request.onlyPopular && !request.searchPattern) {
        hubs = hubs.slice(request.skip);
      }

      if (request.limit && !request.onlyPopular && !request.searchPattern) {
        hubs = hubs.slice(0, request.limit);
      }

      return {
        success: true,
        hubs,
        total: hubs.length,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }
}
