/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import { Hub } from '../../../domain/entities/Hub.js';
import type { IHubRepository } from '../../../domain/repositories/HubRepositories.js';
import { TYPES } from '../../../shared/types/TYPES.js';

export interface GetHubRequest {
  hubId?: string;
  hubName?: string;
  userId?: string; // Optional, for access control
}

export interface GetHubResponse {
  success: boolean;
  hub?: Hub;
  error?: string;
}

@injectable()
export class GetHubUseCase {
  constructor(@inject(TYPES.HubRepository) private hubRepository: IHubRepository) {}

  async execute(request: GetHubRequest): Promise<GetHubResponse> {
    try {
      if (!request.hubId && !request.hubName) {
        return {
          success: false,
          error: 'Either hub ID or hub name must be provided',
        };
      }

      let hub: Hub | null = null;

      if (request.hubId) {
        hub = await this.hubRepository.findById(request.hubId);
      } else if (request.hubName) {
        hub = await this.hubRepository.findByName(request.hubName);
      }

      if (!hub) {
        return {
          success: false,
          error: 'Hub not found',
        };
      }

      // Check access permissions for private hubs
      if (hub.isPrivate && request.userId && !hub.isOwner(request.userId)) {
        // TODO: In the future, check if user is a moderator or has access
        return {
          success: false,
          error: 'This hub is private and you do not have access',
        };
      }

      // Mark hub as active (last accessed)
      hub.markActive();
      await this.hubRepository.save(hub);

      return {
        success: true,
        hub,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }
}
