/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import { Hub, type HubUpdateData } from '../../../domain/entities/Hub.js';
import type { IHubRepository } from '../../../domain/repositories/HubRepositories.js';
import type { IEventBus } from '../../../infrastructure/events/EventBus.js';
import { TYPES } from '../../../shared/types/TYPES.js';

export interface UpdateHubRequest {
  hubId: string;
  userId: string; // User making the request (for authorization)
  updateData: HubUpdateData;
}

export interface UpdateHubResponse {
  success: boolean;
  hub?: Hub;
  error?: string;
}

@injectable()
export class UpdateHubUseCase {
  constructor(
    @inject(TYPES.HubRepository) private hubRepository: IHubRepository,
    @inject(TYPES.EventBus) private eventBus: IEventBus,
  ) {}

  async execute(request: UpdateHubRequest): Promise<UpdateHubResponse> {
    try {
      // Find the hub
      const hub = await this.hubRepository.findById(request.hubId);
      if (!hub) {
        return {
          success: false,
          error: 'Hub not found',
        };
      }

      // Check if user has permission to update the hub
      if (!hub.isOwner(request.userId)) {
        return {
          success: false,
          error: 'You do not have permission to update this hub',
        };
      }

      // Validate update data
      const validation = this.validateUpdateData(request.updateData);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      // Update the hub
      hub.update(request.updateData);

      // Save to repository
      await this.hubRepository.save(hub);

      // Publish domain events
      const events = hub.getDomainEvents();
      for (const event of events) {
        await this.eventBus.publish(event);
      }
      hub.clearDomainEvents();

      return {
        success: true,
        hub,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }

  private validateUpdateData(updateData: HubUpdateData): { isValid: boolean; error?: string } {
    if (updateData.description !== undefined) {
      if (updateData.description.length > 500) {
        return { isValid: false, error: 'Hub description cannot exceed 500 characters' };
      }
      if (updateData.description.trim().length === 0) {
        return { isValid: false, error: 'Hub description cannot be empty' };
      }
    }

    if (updateData.welcomeMessage !== undefined && updateData.welcomeMessage !== null) {
      if (updateData.welcomeMessage.length > 1000) {
        return { isValid: false, error: 'Welcome message cannot exceed 1000 characters' };
      }
    }

    if (updateData.appealCooldownHours !== undefined) {
      if (updateData.appealCooldownHours < 1 || updateData.appealCooldownHours > 168) {
        return {
          isValid: false,
          error: 'Appeal cooldown must be between 1 and 168 hours (1 week)'
        };
      }
    }

    return { isValid: true };
  }
}
