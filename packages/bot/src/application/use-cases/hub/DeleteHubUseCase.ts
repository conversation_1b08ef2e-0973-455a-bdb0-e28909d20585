/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import type { IHubRepository } from '../../../domain/repositories/HubRepositories.js';
import type { IEventBus } from '../../../infrastructure/events/EventBus.js';
import { TYPES } from '../../../shared/types/TYPES.js';

export interface DeleteHubRequest {
  hubId: string;
  userId: string; // User making the request (for authorization)
  confirmationText?: string; // Optional confirmation for safety
}

export interface DeleteHubResponse {
  success: boolean;
  error?: string;
}

@injectable()
export class DeleteHubUseCase {
  constructor(
    @inject(TYPES.HubRepository) private hubRepository: IHubRepository,
    @inject(TYPES.EventBus) private eventBus: IEventBus,
  ) {}

  async execute(request: DeleteHubRequest): Promise<DeleteHubResponse> {
    try {
      // Find the hub
      const hub = await this.hubRepository.findById(request.hubId);
      if (!hub) {
        return {
          success: false,
          error: 'Hub not found',
        };
      }

      // Check if user has permission to delete the hub
      if (!hub.isOwner(request.userId)) {
        return {
          success: false,
          error: 'You do not have permission to delete this hub',
        };
      }

      // Check if hub can be deleted (business logic)
      if (!hub.canBeDeleted()) {
        return {
          success: false,
          error: 'This hub cannot be deleted at this time',
        };
      }

      // Optional confirmation check
      if (request.confirmationText && request.confirmationText !== hub.name) {
        return {
          success: false,
          error: 'Confirmation text does not match the hub name',
        };
      }

      // Delete the hub
      await this.hubRepository.delete(hub.id);

      // TODO: Publish HubDeletedEvent when it's implemented
      // const deleteEvent = new HubDeletedEvent(hub.id, hub.name, hub.ownerId);
      // await this.eventBus.publish(deleteEvent);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }
}
