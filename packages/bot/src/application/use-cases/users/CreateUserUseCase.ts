/**
 * Copyright (c) 2024 InterChat
 * 
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Create User Use Case
 *
 * Handles user creation requests from the application layer.
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { UserDomainService } from '../../../domain/services/UserDomainService.js';
import { ApplicationError } from '../../../shared/errors/DomainError.js';

export interface CreateUserCommand {
  readonly id: string;
  readonly name?: string;
  readonly image?: string;
  readonly locale?: string;
  readonly email?: string;
  readonly source: 'command' | 'auto' | 'invite';
  readonly guildId?: string;
}

export interface CreateUserResult {
  readonly userId: string;
  readonly name: string;
  readonly created: boolean;
  readonly isStaff: boolean;
}

/**
 * Use Case: Create User
 *
 * Creates a new user in the system with proper validation and business rules.
 */
@injectable()
export class CreateUserUseCase {
  constructor(
    @inject(TYPES.UserDomainService)
    private readonly userDomainService: UserDomainService
  ) {}

  async execute(command: CreateUserCommand): Promise<CreateUserResult> {
    try {
      // Validate input
      this.validateCommand(command);

      // Create user through domain service
      const user = await this.userDomainService.registerUser({
        id: command.id,
        name: command.name,
        image: command.image,
        locale: command.locale,
        email: command.email,
        source: command.source,
        guildId: command.guildId,
      });

      return {
        userId: user.id,
        name: user.getDisplayName(),
        created: true,
        isStaff: user.isStaff,
      };
    } catch (error) {
      if (error instanceof Error && error.message === 'User already exists') {
        // Return existing user info instead of throwing
        return {
          userId: command.id,
          name: command.name || `User ${command.id}`,
          created: false,
          isStaff: false,
        };
      }

      throw new ApplicationError(
        'Failed to create user',
        'CREATE_USER_FAILED',
        { originalError: error }
      );
    }
  }

  private validateCommand(command: CreateUserCommand): void {
    if (!command.id || command.id.trim().length === 0) {
      throw new ApplicationError(
        'User ID is required',
        'INVALID_USER_ID'
      );
    }

    if (command.id.length > 20) {
      throw new ApplicationError(
        'User ID is too long',
        'INVALID_USER_ID'
      );
    }

    if (command.name && command.name.length > 100) {
      throw new ApplicationError(
        'User name is too long',
        'INVALID_USER_NAME'
      );
    }

    if (command.email && !this.isValidEmail(command.email)) {
      throw new ApplicationError(
        'Invalid email format',
        'INVALID_EMAIL'
      );
    }

    if (command.locale && command.locale.length > 10) {
      throw new ApplicationError(
        'Invalid locale format',
        'INVALID_LOCALE'
      );
    }

    const validSources = ['command', 'auto', 'invite'];
    if (!validSources.includes(command.source)) {
      throw new ApplicationError(
        'Invalid source type',
        'INVALID_SOURCE'
      );
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
