/**
 * Copyright (c) 2024 InterChat
 * 
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Update User Use Case
 *
 * Handles user information updates from the application layer.
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { UserDomainService } from '../../../domain/services/UserDomainService.js';
import { ApplicationError } from '../../../shared/errors/DomainError.js';
import type { HubActivityLevel } from '../../../../../../build/generated/prisma/client/index.js';

export interface UpdateUserCommand {
  readonly userId: string;
  readonly name?: string;
  readonly image?: string;
  readonly locale?: string;
  readonly showBadges?: boolean;
  readonly mentionOnReply?: boolean;
  readonly preferredLanguages?: string[];
  readonly activityLevel?: HubActivityLevel;
  readonly showNsfwHubs?: boolean;
}

export interface UpdateUserResult {
  readonly userId: string;
  readonly name: string;
  readonly updated: boolean;
  readonly changes: string[];
}

/**
 * Use Case: Update User
 *
 * Updates user information with proper validation and business rules.
 */
@injectable()
export class UpdateUserUseCase {
  constructor(
    @inject(TYPES.UserDomainService)
    private readonly userDomainService: UserDomainService
  ) {}

  async execute(command: UpdateUserCommand): Promise<UpdateUserResult> {
    try {
      // Validate input
      this.validateCommand(command);

      // Track changes for response
      const changes: string[] = [];
      if (command.name !== undefined) changes.push('name');
      if (command.image !== undefined) changes.push('image');
      if (command.locale !== undefined) changes.push('locale');
      if (command.showBadges !== undefined) changes.push('showBadges');
      if (command.mentionOnReply !== undefined) changes.push('mentionOnReply');
      if (command.preferredLanguages !== undefined) changes.push('preferredLanguages');
      if (command.activityLevel !== undefined) changes.push('activityLevel');
      if (command.showNsfwHubs !== undefined) changes.push('showNsfwHubs');

      // Update user through domain service
      const user = await this.userDomainService.updateUser(command.userId, {
        name: command.name,
        image: command.image,
        locale: command.locale,
        showBadges: command.showBadges,
        mentionOnReply: command.mentionOnReply,
        preferredLanguages: command.preferredLanguages,
        activityLevel: command.activityLevel,
        showNsfwHubs: command.showNsfwHubs,
      });

      return {
        userId: user.id,
        name: user.getDisplayName(),
        updated: changes.length > 0,
        changes,
      };
    } catch (error) {
      throw new ApplicationError(
        'Failed to update user',
        'UPDATE_USER_FAILED',
        { originalError: error }
      );
    }
  }

  private validateCommand(command: UpdateUserCommand): void {
    if (!command.userId || command.userId.trim().length === 0) {
      throw new ApplicationError(
        'User ID is required',
        'INVALID_USER_ID'
      );
    }

    if (command.userId.length > 20) {
      throw new ApplicationError(
        'User ID is too long',
        'INVALID_USER_ID'
      );
    }

    if (command.name !== undefined) {
      if (command.name.length > 100) {
        throw new ApplicationError(
          'User name is too long',
          'INVALID_USER_NAME'
        );
      }
    }

    if (command.locale !== undefined) {
      if (command.locale.length > 10) {
        throw new ApplicationError(
          'Invalid locale format',
          'INVALID_LOCALE'
        );
      }
    }

    if (command.preferredLanguages !== undefined) {
      if (command.preferredLanguages.length > 10) {
        throw new ApplicationError(
          'Too many preferred languages',
          'INVALID_PREFERRED_LANGUAGES'
        );
      }

      for (const lang of command.preferredLanguages) {
        if (lang.length > 5) {
          throw new ApplicationError(
            'Invalid language code format',
            'INVALID_LANGUAGE_CODE'
          );
        }
      }
    }

    if (command.activityLevel !== undefined) {
      const validLevels = ['LOW', 'MEDIUM', 'HIGH'];
      if (!validLevels.includes(command.activityLevel)) {
        throw new ApplicationError(
          'Invalid activity level',
          'INVALID_ACTIVITY_LEVEL'
        );
      }
    }

    // Check that at least one field is being updated
    const hasUpdates = [
      command.name,
      command.image,
      command.locale,
      command.showBadges,
      command.mentionOnReply,
      command.preferredLanguages,
      command.activityLevel,
      command.showNsfwHubs,
    ].some(field => field !== undefined);

    if (!hasUpdates) {
      throw new ApplicationError(
        'At least one field must be provided for update',
        'NO_UPDATE_FIELDS'
      );
    }
  }
}
