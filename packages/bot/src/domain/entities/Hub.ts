/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
type Hub as PrismaHub,
  HubActivityLevel,
} from '../../../../../build/generated/prisma/client/index.js';
import { DomainEvent } from '../../infrastructure/events/EventBus.js';
import {
  HubCreatedEvent,
  HubUpdatedEvent,
  HubVisibilityChangedEvent,
  HubOwnershipTransferredEvent,
} from '../events/DomainEvents.js';

export interface HubCreationData {
  name: string;
  description: string;
  ownerId: string;
  iconUrl?: string;
  bannerUrl?: string;
}

export interface HubUpdateData {
  description?: string;
  iconUrl?: string;
  bannerUrl?: string;
  welcomeMessage?: string;
  private?: boolean;
  locked?: boolean;
  appealCooldownHours?: number;
  settings?: number;
  nsfw?: boolean;
}

export class Hub {
  private domainEvents: DomainEvent[] = [];
  private readonly data: PrismaHub;

  constructor(data: PrismaHub) {
    this.data = data;
  }

  // Getters for domain properties
  get id(): string {
    return this.data.id;
  }

  get name(): string {
    return this.data.name;
  }

  get description(): string {
    return this.data.description;
  }

  get ownerId(): string {
    return this.data.ownerId;
  }

  get iconUrl(): string | null {
    return this.data.iconUrl;
  }

  get bannerUrl(): string | null {
    return this.data.bannerUrl;
  }

  get welcomeMessage(): string | null {
    return this.data.welcomeMessage;
  }

  get isPrivate(): boolean {
    return this.data.private;
  }

  get isLocked(): boolean {
    return this.data.locked;
  }

  get isNsfw(): boolean {
    return this.data.nsfw;
  }

  get appealCooldownHours(): number {
    return this.data.appealCooldownHours;
  }

  get settings(): number {
    return this.data.settings;
  }

  get rules(): string[] {
    return this.data.rules;
  }

  get createdAt(): Date {
    return this.data.createdAt;
  }

  get lastActive(): Date {
    return this.data.lastActive;
  }

  // Domain behaviors
  static create(creationData: HubCreationData): Hub {
    const hubData: Partial<PrismaHub> = {
      name: creationData.name,
      description: creationData.description,
      ownerId: creationData.ownerId,
      iconUrl: creationData.iconUrl || '',
      bannerUrl: creationData.bannerUrl || null,
      shortDescription: null,
      welcomeMessage: null,
      private: true, // Hubs start as private by default
      locked: false,
      nsfw: false,
      appealCooldownHours: 24, // Default cooldown
      settings: 0, // Default settings as number
      rules: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      lastActive: new Date(),
      verified: false,
      partnered: false,
      featured: false,
      language: null,
      region: null,
      weeklyMessageCount: 0,
      activityLevel: HubActivityLevel.LOW,
    };

    const hub = new Hub(hubData as PrismaHub);
    hub.addDomainEvent(new HubCreatedEvent(hub.id, hub.name, hub.ownerId));
    return hub;
  }

  update(updateData: HubUpdateData): void {
    const changes: Partial<HubUpdateData> = {};

    if (updateData.description !== undefined && updateData.description !== this.data.description) {
      this.data.description = updateData.description;
      changes.description = updateData.description;
    }

    if (updateData.iconUrl !== undefined && updateData.iconUrl !== this.data.iconUrl) {
      this.data.iconUrl = updateData.iconUrl;
      changes.iconUrl = updateData.iconUrl;
    }

    if (updateData.bannerUrl !== undefined && updateData.bannerUrl !== this.data.bannerUrl) {
      this.data.bannerUrl = updateData.bannerUrl;
      changes.bannerUrl = updateData.bannerUrl;
    }

    if (
      updateData.welcomeMessage !== undefined &&
      updateData.welcomeMessage !== this.data.welcomeMessage
    ) {
      this.data.welcomeMessage = updateData.welcomeMessage;
      changes.welcomeMessage = updateData.welcomeMessage;
    }

    if (updateData.private !== undefined && updateData.private !== this.data.private) {
      this.data.private = updateData.private;
      changes.private = updateData.private;
      // Add visibility changed event
      this.addDomainEvent(
        new HubVisibilityChangedEvent(
          this.id,
          this.name,
          updateData.private,
          'system', // TODO: Pass actual user ID
        ),
      );
    }

    if (updateData.locked !== undefined && updateData.locked !== this.data.locked) {
      this.data.locked = updateData.locked;
      changes.locked = updateData.locked;
    }

    if (
      updateData.appealCooldownHours !== undefined &&
      updateData.appealCooldownHours !== this.data.appealCooldownHours
    ) {
      this.data.appealCooldownHours = updateData.appealCooldownHours;
      changes.appealCooldownHours = updateData.appealCooldownHours;
    }

    if (updateData.settings !== undefined && updateData.settings !== this.data.settings) {
      this.data.settings = updateData.settings;
      changes.settings = updateData.settings;
    }

    if (updateData.nsfw !== undefined && updateData.nsfw !== this.data.nsfw) {
      this.data.nsfw = updateData.nsfw;
      changes.nsfw = updateData.nsfw;
    }

    if (Object.keys(changes).length > 0) {
      this.data.updatedAt = new Date();
      // Add hub updated event
      this.addDomainEvent(
        new HubUpdatedEvent(
          this.id,
          this.name,
          'system', // TODO: Pass actual user ID
          Object.keys(changes),
        ),
      );
    }
  }

  updateRules(rules: string[]): void {
    this.data.rules = [...rules];
    this.data.updatedAt = new Date();
    // Add hub updated event for rules
    this.addDomainEvent(new HubUpdatedEvent(
      this.id,
      this.name,
      'system', // TODO: Pass actual user ID
      ['rules']
    ));
  }

  transferOwnership(newOwnerId: string): void {
    const previousOwnerId = this.data.ownerId;
    this.data.ownerId = newOwnerId;
    this.data.updatedAt = new Date();

    // Add ownership transferred event
    this.addDomainEvent(new HubOwnershipTransferredEvent(
      this.id,
      this.name,
      previousOwnerId,
      newOwnerId,
      'system' // TODO: Pass actual user ID
    ));
  }

  markActive(): void {
    this.data.lastActive = new Date();
  }

  isOwner(userId: string): boolean {
    return this.data.ownerId === userId;
  }

  // Domain validation
  canBeDeleted(): boolean {
    // Add business rules for when a hub can be deleted
    return true; // For now, any hub can be deleted
  }

  canBeMadePublic(): boolean {
    // Add business rules for when a hub can be made public
    return this.data.description.length >= 10 && this.data.rules.length > 0;
  }

  // Domain events handling
  private addDomainEvent(event: DomainEvent): void {
    this.domainEvents.push(event);
  }

  getDomainEvents(): DomainEvent[] {
    return [...this.domainEvents];
  }

  clearDomainEvents(): void {
    this.domainEvents = [];
  }

  // For persistence - returns raw data
  toPersistence(): PrismaHub {
    return { ...this.data };
  }

  // For reconstruction from persistence
  static fromPersistence(data: PrismaHub): Hub {
    return new Hub(data);
  }
}
