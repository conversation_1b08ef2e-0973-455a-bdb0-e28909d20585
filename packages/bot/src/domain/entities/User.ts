/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * User Domain Entity
 *
 * Represents a Discord user in the InterChat system with all their
 * preferences, activity tracking, and business logic.
 */

import {
  type User as PrismaUser,
  type HubActivityLevel,
} from '../../../../../build/generated/prisma/client/index.js';
import { DomainEvent } from '../../infrastructure/events/EventBus.js';
import {
  UserCreatedEvent,
  UserUpdatedEvent,
  UserPreferencesChangedEvent,
  UserActivityUpdatedEvent,
  UserBannedEvent,
  UserUnbannedEvent,
} from '../events/DomainEvents.js';
import { BusinessRuleViolationError } from '../../shared/errors/DomainError.js';

export interface UserCreationData {
  id: string;
  name?: string;
  image?: string;
  locale?: string;
  email?: string;
}

export interface UserUpdateData {
  name?: string;
  image?: string;
  locale?: string;
  showBadges?: boolean;
  mentionOnReply?: boolean;
  preferredLanguages?: string[];
  activityLevel?: HubActivityLevel;
  showNsfwHubs?: boolean;
}

export interface UserPreferences {
  showBadges: boolean;
  mentionOnReply: boolean;
  locale: string | null;
  preferredLanguages: string[];
  activityLevel: HubActivityLevel | null;
  showNsfwHubs: boolean;
}

export interface UserActivity {
  messageCount: number;
  lastMessageAt: Date;
  voteCount: number;
  lastVoted: Date | null;
  reputation: number;
  hubJoinCount: number;
  lastHubJoinAt: Date | null;
  hubEngagementScore: number;
}

export interface UserBanInfo {
  reason: string | null;
  bannedAt: Date | null;
  isActive: boolean;
}

/**
 * User Entity
 *
 * Manages user data, preferences, activity tracking, and business rules.
 */
export class User {
  private domainEvents: DomainEvent[] = [];
  private data: PrismaUser;

  private constructor(userData: PrismaUser) {
    this.data = userData;
    this.validateUser();
  }

  /**
   * Create new user
   */
  static create(creationData: UserCreationData): User {
    const userData: Partial<PrismaUser> = {
      id: creationData.id,
      name: creationData.name || null,
      image: creationData.image || null,
      locale: creationData.locale || null,
      email: creationData.email || null,
      showBadges: true,
      mentionOnReply: true,
      voteCount: 0,
      reputation: 0,
      lastVoted: null,
      banReason: null,
      messageCount: 0,
      lastMessageAt: new Date(),
      inboxLastReadDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      isStaff: false,
      emailVerified: null,
      donationEmail: null,
      preferredLanguages: [],
      activityLevel: null,
      showNsfwHubs: false,
      donationExpiresAt: null,
      hubJoinCount: 0,
      lastHubJoinAt: null,
      hubEngagementScore: 0.0,
    };

    const user = new User(userData as PrismaUser);

    user.addDomainEvent(new UserCreatedEvent(
      creationData.id,
      creationData.name || 'Unknown User',
      creationData.email || null
    ));

    return user;
  }

  /**
   * Reconstitute from persistence
   */
  static fromPersistence(userData: PrismaUser): User {
    return new User(userData);
  }

  // Getters
  get id(): string {
    return this.data.id;
  }

  get name(): string | null {
    return this.data.name;
  }

  get image(): string | null {
    return this.data.image;
  }

  get email(): string | null {
    return this.data.email;
  }

  get isStaff(): boolean {
    return this.data.isStaff;
  }

  get createdAt(): Date {
    return this.data.createdAt;
  }

  get updatedAt(): Date {
    return this.data.updatedAt;
  }

  get preferences(): UserPreferences {
    return {
      showBadges: this.data.showBadges,
      mentionOnReply: this.data.mentionOnReply,
      locale: this.data.locale,
      preferredLanguages: this.data.preferredLanguages,
      activityLevel: this.data.activityLevel,
      showNsfwHubs: this.data.showNsfwHubs,
    };
  }

  get activity(): UserActivity {
    return {
      messageCount: this.data.messageCount,
      lastMessageAt: this.data.lastMessageAt,
      voteCount: this.data.voteCount,
      lastVoted: this.data.lastVoted,
      reputation: this.data.reputation,
      hubJoinCount: this.data.hubJoinCount,
      lastHubJoinAt: this.data.lastHubJoinAt,
      hubEngagementScore: this.data.hubEngagementScore,
    };
  }

  get banInfo(): UserBanInfo {
    return {
      reason: this.data.banReason,
      bannedAt: this.data.banReason ? this.data.updatedAt : null,
      isActive: this.data.banReason !== null,
    };
  }

  get isBanned(): boolean {
    return this.data.banReason !== null;
  }

  /**
   * Update user information
   */
  update(updateData: UserUpdateData): void {
    const oldData = { ...this.data };
    let hasChanges = false;

    if (updateData.name !== undefined && updateData.name !== this.data.name) {
      this.data.name = updateData.name;
      hasChanges = true;
    }

    if (updateData.image !== undefined && updateData.image !== this.data.image) {
      this.data.image = updateData.image;
      hasChanges = true;
    }

    if (updateData.locale !== undefined && updateData.locale !== this.data.locale) {
      this.data.locale = updateData.locale;
      hasChanges = true;
    }

    if (hasChanges) {
      this.data.updatedAt = new Date();
      this.addDomainEvent(new UserUpdatedEvent(
        this.data.id,
        {
          name: oldData.name,
          image: oldData.image,
          locale: oldData.locale,
        },
        {
          name: this.data.name,
          image: this.data.image,
          locale: this.data.locale,
        }
      ));
    }

    // Handle preferences separately
    this.updatePreferences({
      showBadges: updateData.showBadges,
      mentionOnReply: updateData.mentionOnReply,
      preferredLanguages: updateData.preferredLanguages,
      activityLevel: updateData.activityLevel,
      showNsfwHubs: updateData.showNsfwHubs,
    });
  }

  /**
   * Update user preferences
   */
  updatePreferences(preferences: Partial<UserPreferences>): void {
    const oldPreferences = this.preferences;
    let hasChanges = false;

    if (preferences.showBadges !== undefined && preferences.showBadges !== this.data.showBadges) {
      this.data.showBadges = preferences.showBadges;
      hasChanges = true;
    }

    if (preferences.mentionOnReply !== undefined && preferences.mentionOnReply !== this.data.mentionOnReply) {
      this.data.mentionOnReply = preferences.mentionOnReply;
      hasChanges = true;
    }

    if (preferences.preferredLanguages !== undefined) {
      const newLangs = JSON.stringify(preferences.preferredLanguages.sort());
      const oldLangs = JSON.stringify(this.data.preferredLanguages.sort());
      if (newLangs !== oldLangs) {
        this.data.preferredLanguages = preferences.preferredLanguages;
        hasChanges = true;
      }
    }

    if (preferences.activityLevel !== undefined && preferences.activityLevel !== this.data.activityLevel) {
      this.data.activityLevel = preferences.activityLevel;
      hasChanges = true;
    }

    if (preferences.showNsfwHubs !== undefined && preferences.showNsfwHubs !== this.data.showNsfwHubs) {
      this.data.showNsfwHubs = preferences.showNsfwHubs;
      hasChanges = true;
    }

    if (hasChanges) {
      this.data.updatedAt = new Date();
      this.addDomainEvent(new UserPreferencesChangedEvent(
        this.data.id,
        oldPreferences,
        this.preferences
      ));
    }
  }

  /**
   * Record user activity (message sent)
   */
  recordMessageActivity(): void {
    this.data.messageCount += 1;
    this.data.lastMessageAt = new Date();
    this.data.updatedAt = new Date();

    this.addDomainEvent(new UserActivityUpdatedEvent(
      this.data.id,
      'message',
      this.data.messageCount
    ));
  }

  /**
   * Record user vote
   */
  recordVote(): void {
    if (this.canVoteToday()) {
      throw new BusinessRuleViolationError('User has already voted today');
    }

    this.data.voteCount += 1;
    this.data.lastVoted = new Date();
    this.data.updatedAt = new Date();

    this.addDomainEvent(new UserActivityUpdatedEvent(
      this.data.id,
      'vote',
      this.data.voteCount
    ));
  }

  /**
   * Update reputation
   */
  updateReputation(change: number, reason: string): void {
    const oldReputation = this.data.reputation;
    this.data.reputation += change;
    this.data.updatedAt = new Date();

    this.addDomainEvent(new UserActivityUpdatedEvent(
      this.data.id,
      'reputation',
      this.data.reputation,
      { change, reason, oldValue: oldReputation }
    ));
  }

  /**
   * Record hub join activity
   */
  recordHubJoin(): void {
    this.data.hubJoinCount += 1;
    this.data.lastHubJoinAt = new Date();
    this.data.updatedAt = new Date();

    this.addDomainEvent(new UserActivityUpdatedEvent(
      this.data.id,
      'hub_join',
      this.data.hubJoinCount
    ));
  }

  /**
   * Update hub engagement score
   */
  updateHubEngagementScore(score: number): void {
    this.data.hubEngagementScore = score;
    this.data.updatedAt = new Date();

    this.addDomainEvent(new UserActivityUpdatedEvent(
      this.data.id,
      'engagement',
      score
    ));
  }

  /**
   * Ban user
   */
  ban(reason: string, moderatorId: string): void {
    if (this.isBanned) {
      throw new BusinessRuleViolationError('User is already banned');
    }

    this.data.banReason = reason;
    this.data.updatedAt = new Date();

    this.addDomainEvent(new UserBannedEvent(
      this.data.id,
      reason,
      moderatorId
    ));
  }

  /**
   * Unban user
   */
  unban(moderatorId: string): void {
    if (!this.isBanned) {
      throw new BusinessRuleViolationError('User is not banned');
    }

    const previousReason = this.data.banReason;
    this.data.banReason = null;
    this.data.updatedAt = new Date();

    this.addDomainEvent(new UserUnbannedEvent(
      this.data.id,
      previousReason!,
      moderatorId
    ));
  }

  /**
   * Grant staff privileges
   */
  grantStaffPrivileges(grantedBy: string): void {
    if (this.data.isStaff) {
      throw new BusinessRuleViolationError('User is already staff');
    }

    this.data.isStaff = true;
    this.data.updatedAt = new Date();

    this.addDomainEvent(new UserUpdatedEvent(
      this.data.id,
      { isStaff: false },
      { isStaff: true },
      { grantedBy, action: 'staff_granted' }
    ));
  }

  /**
   * Revoke staff privileges
   */
  revokeStaffPrivileges(revokedBy: string): void {
    if (!this.data.isStaff) {
      throw new BusinessRuleViolationError('User is not staff');
    }

    this.data.isStaff = false;
    this.data.updatedAt = new Date();

    this.addDomainEvent(new UserUpdatedEvent(
      this.data.id,
      { isStaff: true },
      { isStaff: false },
      { revokedBy, action: 'staff_revoked' }
    ));
  }

  /**
   * Check if user can vote today
   */
  canVoteToday(): boolean {
    if (!this.data.lastVoted) return true;

    const VOTE_COOLDOWN_MS = 24 * 60 * 60 * 1000; // 24 hours
    const timeSinceVote = Date.now() - this.data.lastVoted.getTime();
    return timeSinceVote >= VOTE_COOLDOWN_MS;
  }

  /**
   * Get user's display name
   */
  getDisplayName(): string {
    return this.data.name || `User ${this.data.id}`;
  }

  /**
   * Check if user has specific preference
   */
  hasPreference(preference: keyof UserPreferences): boolean {
    const prefs = this.preferences;
    const value = prefs[preference];

    if (typeof value === 'boolean') {
      return value;
    }

    if (Array.isArray(value)) {
      return value.length > 0;
    }

    return value !== null && value !== undefined;
  }

  /**
   * Get domain events and clear them
   */
  getDomainEvents(): DomainEvent[] {
    const events = [...this.domainEvents];
    this.domainEvents = [];
    return events;
  }

  /**
   * Convert to persistence format
   */
  toPersistence(): PrismaUser {
    return { ...this.data };
  }

  private addDomainEvent(event: DomainEvent): void {
    this.domainEvents.push(event);
  }

  private validateUser(): void {
    if (!this.data.id || this.data.id.trim().length === 0) {
      throw new BusinessRuleViolationError('User ID cannot be empty');
    }

    if (this.data.voteCount < 0) {
      throw new BusinessRuleViolationError('Vote count cannot be negative');
    }

    if (this.data.messageCount < 0) {
      throw new BusinessRuleViolationError('Message count cannot be negative');
    }

    if (this.data.hubJoinCount < 0) {
      throw new BusinessRuleViolationError('Hub join count cannot be negative');
    }

    if (this.data.hubEngagementScore < 0) {
      throw new BusinessRuleViolationError('Hub engagement score cannot be negative');
    }
  }
}
