/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { DomainEvent } from '../../infrastructure/events/EventBus.js';

/**
 * Hub Created Event
 *
 * This event is published when a new hub is created.
 * It's set to broadcast across clusters since other clusters
 * need to know about new hubs for routing messages.
 */
export class HubCreatedEvent extends DomainEvent {
  constructor(
    public readonly hubId: string,
    public readonly hubName: string,
    public readonly createdBy: string,
    public readonly serverCount: number = 0,
    clusterId?: string
  ) {
    super(
      'hub.created',
      hubId,
      1,
      true, // Broadcast to all clusters
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      hubId: this.hubId,
      hubName: this.hubName,
      createdBy: this.createdBy,
      serverCount: this.serverCount,
    };
  }

  static fromJSON(json: Record<string, any>): HubCreatedEvent {
    const data = json.data;
    const event = new HubCreatedEvent(
      data.hubId,
      data.hubName,
      data.createdBy,
      data.serverCount
    );

    // Restore the original properties
    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * Server Connected to Hub Event
 *
 * This event is published when a server connects to a hub.
 * It's set to broadcast since all clusters need to know about
 * server connections for message routing.
 */
export class ServerConnectedToHubEvent extends DomainEvent {
  constructor(
    public readonly hubId: string,
    public readonly serverId: string,
    public readonly serverName: string,
    public readonly connectedBy: string
  ) {
    super(
      'hub.server.connected',
      hubId,
      1,
      true // Broadcast to all clusters
    );
  }

  protected getData(): Record<string, any> {
    return {
      hubId: this.hubId,
      serverId: this.serverId,
      serverName: this.serverName,
      connectedBy: this.connectedBy,
    };
  }

  static fromJSON(json: Record<string, any>): ServerConnectedToHubEvent {
    const data = json.data;
    const event = new ServerConnectedToHubEvent(
      data.hubId,
      data.serverId,
      data.serverName,
      data.connectedBy
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * User Premium Status Changed Event
 *
 * This event is published when a user's premium status changes.
 * It's set to broadcast since all clusters need to know about
 * premium status for feature access control.
 */
export class UserPremiumStatusChangedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly isPremium: boolean,
    public readonly tier: string | null,
    public readonly expiresAt: Date | null
  ) {
    super(
      'user.premium.status.changed',
      userId,
      1,
      true // Broadcast to all clusters
    );
  }

  protected getData(): Record<string, any> {
    return {
      userId: this.userId,
      isPremium: this.isPremium,
      tier: this.tier,
      expiresAt: this.expiresAt?.toISOString() || null,
    };
  }

  static fromJSON(json: Record<string, any>): UserPremiumStatusChangedEvent {
    const data = json.data;
    const event = new UserPremiumStatusChangedEvent(
      data.userId,
      data.isPremium,
      data.tier,
      data.expiresAt ? new Date(data.expiresAt) : null
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * Command Executed Event
 *
 * This event is published when a command is executed.
 * It's NOT set to broadcast since command execution
 * tracking is typically local to the cluster.
 */
export class CommandExecutedEvent extends DomainEvent {
  constructor(
    public readonly commandName: string,
    public readonly userId: string,
    public readonly guildId: string | null,
    public readonly executionTimeMs: number,
    public readonly success: boolean,
    public readonly errorMessage?: string
  ) {
    super(
      'command.executed',
      `${commandName}-${userId}-${Date.now()}`,
      1,
      false // Local to cluster only
    );
  }

  protected getData(): Record<string, any> {
    return {
      commandName: this.commandName,
      userId: this.userId,
      guildId: this.guildId,
      executionTimeMs: this.executionTimeMs,
      success: this.success,
      errorMessage: this.errorMessage,
    };
  }

  static fromJSON(json: Record<string, any>): CommandExecutedEvent {
    const data = json.data;
    const event = new CommandExecutedEvent(
      data.commandName,
      data.userId,
      data.guildId,
      data.executionTimeMs,
      data.success,
      data.errorMessage
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * Donation Created Event
 *
 * Published when a new donation is created and processed.
 * Broadcast to all clusters for analytics and notifications.
 */
export class DonationCreatedEvent extends DomainEvent {
  constructor(
    public readonly donationId: string,
    public readonly donorId: string,
    public readonly amount: number,
    public readonly currency: string,
    public readonly tier: number,
    public readonly premiumGranted: boolean,
    clusterId?: string
  ) {
    super(
      'donation.created',
      donationId,
      1,
      true, // Broadcast to all clusters
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      donationId: this.donationId,
      donorId: this.donorId,
      amount: this.amount,
      currency: this.currency,
      tier: this.tier,
      premiumGranted: this.premiumGranted,
    };
  }
}

/**
 * Premium Granted Event
 *
 * Published when premium status is granted to a user.
 * Broadcast to all clusters for status synchronization.
 */
export class PremiumGrantedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly tier: number,
    public readonly expiresAt: Date | null,
    public readonly grantedBy: string,
    public readonly reason: string,
    clusterId?: string
  ) {
    super(
      'premium.granted',
      userId,
      1,
      true, // Broadcast to all clusters
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      userId: this.userId,
      tier: this.tier,
      expiresAt: this.expiresAt?.toISOString(),
      grantedBy: this.grantedBy,
      reason: this.reason,
    };
  }
}

/**
 * Premium Expired Event
 *
 * Published when a user's premium status expires.
 * Broadcast to all clusters for status synchronization.
 */
export class PremiumExpiredEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly previousTier: number,
    clusterId?: string
  ) {
    super(
      'premium.expired',
      userId,
      1,
      true, // Broadcast to all clusters
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      userId: this.userId,
      previousTier: this.previousTier,
    };
  }
}

/**
 * Hub Updated Event
 *
 * This event is published when a hub is updated.
 * It's set to broadcast across clusters since other clusters
 * need to know about hub changes.
 */
export class HubUpdatedEvent extends DomainEvent {
  constructor(
    public readonly hubId: string,
    public readonly hubName: string,
    public readonly updatedBy: string,
    public readonly updatedFields: string[],
    clusterId?: string
  ) {
    super(
      'hub.updated',
      hubId,
      1,
      true, // Broadcast to all clusters
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      hubId: this.hubId,
      hubName: this.hubName,
      updatedBy: this.updatedBy,
      updatedFields: this.updatedFields,
    };
  }

  static fromJSON(json: Record<string, any>): HubUpdatedEvent {
    const data = json.data;
    const event = new HubUpdatedEvent(
      data.hubId,
      data.hubName,
      data.updatedBy,
      data.updatedFields
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * Hub Deleted Event
 *
 * This event is published when a hub is deleted.
 * It's set to broadcast across clusters since other clusters
 * need to know about hub deletions for cleanup.
 */
export class HubDeletedEvent extends DomainEvent {
  constructor(
    public readonly hubId: string,
    public readonly hubName: string,
    public readonly deletedBy: string,
    clusterId?: string
  ) {
    super(
      'hub.deleted',
      hubId,
      1,
      true, // Broadcast to all clusters
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      hubId: this.hubId,
      hubName: this.hubName,
      deletedBy: this.deletedBy,
    };
  }

  static fromJSON(json: Record<string, any>): HubDeletedEvent {
    const data = json.data;
    const event = new HubDeletedEvent(
      data.hubId,
      data.hubName,
      data.deletedBy
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * Hub Visibility Changed Event
 *
 * This event is published when a hub's visibility changes (private/public).
 * It's set to broadcast since other clusters need to know about
 * visibility changes for access control.
 */
export class HubVisibilityChangedEvent extends DomainEvent {
  constructor(
    public readonly hubId: string,
    public readonly hubName: string,
    public readonly isPrivate: boolean,
    public readonly changedBy: string,
    clusterId?: string
  ) {
    super(
      'hub.visibility.changed',
      hubId,
      1,
      true, // Broadcast to all clusters
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      hubId: this.hubId,
      hubName: this.hubName,
      isPrivate: this.isPrivate,
      changedBy: this.changedBy,
    };
  }

  static fromJSON(json: Record<string, any>): HubVisibilityChangedEvent {
    const data = json.data;
    const event = new HubVisibilityChangedEvent(
      data.hubId,
      data.hubName,
      data.isPrivate,
      data.changedBy
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * Hub Ownership Transferred Event
 *
 * This event is published when a hub's ownership is transferred.
 * It's set to broadcast since other clusters need to know about
 * ownership changes for permissions.
 */
export class HubOwnershipTransferredEvent extends DomainEvent {
  constructor(
    public readonly hubId: string,
    public readonly hubName: string,
    public readonly previousOwnerId: string,
    public readonly newOwnerId: string,
    public readonly transferredBy: string,
    clusterId?: string
  ) {
    super(
      'hub.ownership.transferred',
      hubId,
      1,
      true, // Broadcast to all clusters
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      hubId: this.hubId,
      hubName: this.hubName,
      previousOwnerId: this.previousOwnerId,
      newOwnerId: this.newOwnerId,
      transferredBy: this.transferredBy,
    };
  }

  static fromJSON(json: Record<string, any>): HubOwnershipTransferredEvent {
    const data = json.data;
    const event = new HubOwnershipTransferredEvent(
      data.hubId,
      data.hubName,
      data.previousOwnerId,
      data.newOwnerId,
      data.transferredBy
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * User Created Event
 *
 * Published when a new user is created in the system.
 */
export class UserCreatedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly userName: string,
    public readonly email: string | null,
    clusterId?: string
  ) {
    super(
      'user.created',
      userId,
      1,
      true, // Broadcast to all clusters
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      userId: this.userId,
      userName: this.userName,
      email: this.email,
    };
  }

  static fromJSON(json: Record<string, any>): UserCreatedEvent {
    const data = json.data;
    const event = new UserCreatedEvent(
      data.userId,
      data.userName,
      data.email
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * User Updated Event
 *
 * Published when user information is updated.
 */
export class UserUpdatedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly previousData: Record<string, any>,
    public readonly newData: Record<string, any>,
    public readonly metadata?: Record<string, any>,
    clusterId?: string
  ) {
    super(
      'user.updated',
      userId,
      1,
      false, // Local cluster only
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      userId: this.userId,
      previousData: this.previousData,
      newData: this.newData,
      metadata: this.metadata,
    };
  }

  static fromJSON(json: Record<string, any>): UserUpdatedEvent {
    const data = json.data;
    const event = new UserUpdatedEvent(
      data.userId,
      data.previousData,
      data.newData,
      data.metadata
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * User Preferences Changed Event
 *
 * Published when user preferences are updated.
 */
export class UserPreferencesChangedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly previousPreferences: Record<string, any>,
    public readonly newPreferences: Record<string, any>,
    clusterId?: string
  ) {
    super(
      'user.preferences.changed',
      userId,
      1,
      false, // Local cluster only
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      userId: this.userId,
      previousPreferences: this.previousPreferences,
      newPreferences: this.newPreferences,
    };
  }

  static fromJSON(json: Record<string, any>): UserPreferencesChangedEvent {
    const data = json.data;
    const event = new UserPreferencesChangedEvent(
      data.userId,
      data.previousPreferences,
      data.newPreferences
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * User Activity Updated Event
 *
 * Published when user activity metrics are updated.
 */
export class UserActivityUpdatedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly activityType: string,
    public readonly newValue: number,
    public readonly metadata?: Record<string, any>,
    clusterId?: string
  ) {
    super(
      'user.activity.updated',
      userId,
      1,
      false, // Local cluster only
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      userId: this.userId,
      activityType: this.activityType,
      newValue: this.newValue,
      metadata: this.metadata,
    };
  }

  static fromJSON(json: Record<string, any>): UserActivityUpdatedEvent {
    const data = json.data;
    const event = new UserActivityUpdatedEvent(
      data.userId,
      data.activityType,
      data.newValue,
      data.metadata
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * User Banned Event
 *
 * Published when a user is banned from the system.
 */
export class UserBannedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly reason: string,
    public readonly moderatorId: string,
    clusterId?: string
  ) {
    super(
      'user.banned',
      userId,
      1,
      true, // Broadcast to all clusters
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      userId: this.userId,
      reason: this.reason,
      moderatorId: this.moderatorId,
    };
  }

  static fromJSON(json: Record<string, any>): UserBannedEvent {
    const data = json.data;
    const event = new UserBannedEvent(
      data.userId,
      data.reason,
      data.moderatorId
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}

/**
 * User Unbanned Event
 *
 * Published when a user is unbanned from the system.
 */
export class UserUnbannedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly previousReason: string,
    public readonly moderatorId: string,
    clusterId?: string
  ) {
    super(
      'user.unbanned',
      userId,
      1,
      true, // Broadcast to all clusters
      clusterId
    );
  }

  protected getData(): Record<string, any> {
    return {
      userId: this.userId,
      previousReason: this.previousReason,
      moderatorId: this.moderatorId,
    };
  }

  static fromJSON(json: Record<string, any>): UserUnbannedEvent {
    const data = json.data;
    const event = new UserUnbannedEvent(
      data.userId,
      data.previousReason,
      data.moderatorId
    );

    Object.assign(event, {
      eventId: json.eventId,
      clusterId: json.clusterId,
      occurredAt: new Date(json.occurredAt),
    });

    return event;
  }
}
