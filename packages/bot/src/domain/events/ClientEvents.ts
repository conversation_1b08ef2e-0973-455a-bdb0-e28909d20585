/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { DomainEvent } from '../../infrastructure/events/EventBus.js';

/**
 * Client Ready Event
 *
 * Emitted when the Discord client successfully connects and is ready to serve
 */
export class ClientReadyEvent extends DomainEvent {
  constructor(
    public readonly clusterId: string,
    public readonly guilds: number,
    public readonly users: number,
    public readonly shards: number[],
    shouldBroadcast: boolean = true
  ) {
    super('client.ready', clusterId, 1, shouldBroadcast, clusterId);
  }

  protected getData(): Record<string, any> {
    return {
      clusterId: this.clusterId,
      guilds: this.guilds,
      users: this.users,
      shards: this.shards,
    };
  }
}

/**
 * Client Shutdown Event
 *
 * Emitted when the Discord client is shutting down gracefully
 */
export class ClientShutdownEvent extends DomainEvent {
  constructor(
    public readonly clusterId: string,
    public readonly reason?: string,
    shouldBroadcast: boolean = true
  ) {
    super('client.shutdown', clusterId, 1, shouldBroadcast, clusterId);
  }

  protected getData(): Record<string, any> {
    return {
      clusterId: this.clusterId,
      reason: this.reason,
    };
  }
}

/**
 * Guild Joined Event
 *
 * Emitted when the bot joins a new guild
 */
export class GuildJoinedEvent extends DomainEvent {
  constructor(
    public readonly guildId: string,
    public readonly guildName: string,
    public readonly memberCount: number,
    public readonly clusterId: string,
    shouldBroadcast: boolean = true
  ) {
    super('guild.joined', guildId, 1, shouldBroadcast, clusterId);
  }

  protected getData(): Record<string, any> {
    return {
      guildId: this.guildId,
      guildName: this.guildName,
      memberCount: this.memberCount,
      clusterId: this.clusterId,
    };
  }
}

/**
 * Guild Left Event
 *
 * Emitted when the bot leaves a guild
 */
export class GuildLeftEvent extends DomainEvent {
  constructor(
    public readonly guildId: string,
    public readonly guildName: string,
    public readonly clusterId: string,
    shouldBroadcast: boolean = true
  ) {
    super('guild.left', guildId, 1, shouldBroadcast, clusterId);
  }

  protected getData(): Record<string, any> {
    return {
      guildId: this.guildId,
      guildName: this.guildName,
      clusterId: this.clusterId,
    };
  }
}
