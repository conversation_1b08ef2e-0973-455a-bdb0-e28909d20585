/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../shared/types/TYPES.js';
import type { IEventBus } from '../../infrastructure/events/TestInterfaces.js';
import { BusinessRuleViolationError, NotFoundError } from '../../shared/errors/DomainError.js';

import {
  Donation,
  UserPremiumStatus,
  DonationSource
} from '../entities/Donation.js';
import { Money, DonationTier } from '../value-objects/DonationValueObjects.js';
import {
  type IDonationRepository,
  type IUserPremiumRepository
} from '../repositories/DonationRepositories.js';

/**
 * Ko-fi Webhook Payload Interface
 */
export interface KofiDonationPayload {
  verification_token: string;
  message_id: string;
  timestamp: string;
  type: 'Donation' | 'Subscription';
  is_public: boolean;
  from_name: string;
  message: string;
  amount: string;
  url: string;
  email: string;
  currency: string;
  is_subscription_payment: boolean;
  is_first_subscription_payment: boolean;
  kofi_transaction_id: string;
  shop_items?: any[];
  tier_name?: string;
}

/**
 * Domain Service for Donation Management
 *
 * Contains business logic that doesn't naturally fit within a single entity.
 * Coordinates between entities and ensures business rules are enforced.
 */
@injectable()
export class DonationDomainService {
  constructor(
    @inject(TYPES.DonationRepository) private donationRepository: IDonationRepository,
    @inject(TYPES.UserPremiumRepository) private userPremiumRepository: IUserPremiumRepository,
    @inject(TYPES.EventBus) private eventBus: IEventBus
  ) {}

  /**
   * Process a Ko-fi donation webhook
   */
  async processKofiDonation(
    payload: KofiDonationPayload,
    discordUserId: string
  ): Promise<Donation> {
    // Validate webhook payload
    this.validateKofiPayload(payload);

    // Check for duplicate donation
    const existingDonation = await this.donationRepository.findBySourceTransactionId(
      payload.kofi_transaction_id,
      DonationSource.KOFI
    );

    if (existingDonation) {
      throw new BusinessRuleViolationError(
        `Donation with Ko-fi transaction ID ${payload.kofi_transaction_id} already exists`
      );
    }

    // Create donation entity
    const amount = Money.create(parseFloat(payload.amount), payload.currency.toUpperCase());
    const donation = Donation.create(
      crypto.randomUUID(),
      discordUserId,
      amount,
      DonationSource.KOFI,
      payload.kofi_transaction_id,
      new Date(payload.timestamp),
      payload.message,
      payload.from_name,
      payload.email,
      {
        messageId: payload.message_id,
        isPublic: payload.is_public,
        isSubscription: payload.is_subscription_payment,
        isFirstSubscription: payload.is_first_subscription_payment,
        tierName: payload.tier_name,
      }
    );

    // Mark as completed (Ko-fi donations are pre-approved)
    donation.markAsCompleted();

    // Save donation
    await this.donationRepository.save(donation);

    // Process premium status
    await this.processPremiumStatusChange(discordUserId, donation);

    // Publish domain events
    const events = donation.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }

    return donation;
  }

  /**
   * Check if a user has a specific premium tier
   */
  async hasPremiumTier(userId: string, tierName: string): Promise<boolean> {
    const premiumStatus = await this.userPremiumRepository.findByUserId(userId);

    if (!premiumStatus || !premiumStatus.isActive) {
      return false;
    }

    return premiumStatus.tier?.name === tierName;
  }

  /**
   * Check if user has Ko-fi Supporter tier
   */
  async hasKofiSupporterTier(userId: string): Promise<boolean> {
    return this.hasPremiumTier(userId, 'Ko-fi Supporter');
  }

  /**
   * Get user's total donated amount
   */
  async getUserTotalDonated(userId: string): Promise<Money> {
    return this.donationRepository.getTotalDonatedByUser(userId);
  }

  /**
   * Revoke user's premium status
   */
  async revokePremiumStatus(userId: string, reason?: string): Promise<void> {
    const premiumStatus = await this.userPremiumRepository.findByUserId(userId);

    if (!premiumStatus) {
      throw new NotFoundError('UserPremiumStatus', userId);
    }

    premiumStatus.revokePremiumStatus(reason);

    await this.userPremiumRepository.save(premiumStatus);

    // Publish domain events
    const events = premiumStatus.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Grant premium tier manually (admin function)
   */
  async grantPremiumTier(
    userId: string,
    tierName: string,
    expiresAt?: Date
  ): Promise<void> {
    let premiumStatus = await this.userPremiumRepository.findByUserId(userId);

    if (!premiumStatus) {
      premiumStatus = UserPremiumStatus.create(userId);
    }

    const tier = this.getTierByName(tierName);
    premiumStatus.grantPremiumTier(tier, expiresAt);

    await this.userPremiumRepository.save(premiumStatus);

    // Publish domain events
    const events = premiumStatus.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Process expired premium subscriptions
   */
  async processExpiredPremium(): Promise<void> {
    const expiredUsers = await this.userPremiumRepository.findExpiredPremium();

    for (const premiumStatus of expiredUsers) {
      premiumStatus.revokePremiumStatus('Subscription expired');

      await this.userPremiumRepository.save(premiumStatus);

      // Publish domain events
      const events = premiumStatus.getDomainEvents();
      for (const event of events) {
        await this.eventBus.publish(event);
      }
    }
  }

  /**
   * Refund a donation
   */
  async refundDonation(donationId: string, reason?: string): Promise<void> {
    const donation = await this.donationRepository.findById(donationId);

    if (!donation) {
      throw new NotFoundError('Donation', donationId);
    }

    donation.refund(reason);

    await this.donationRepository.save(donation);

    // Re-evaluate user's premium status
    await this.reevaluatePremiumStatus(donation.userId);

    // Publish domain events
    const events = donation.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Process premium status change after donation
   */
  private async processPremiumStatusChange(
    userId: string,
    donation: Donation
  ): Promise<void> {
    let premiumStatus = await this.userPremiumRepository.findByUserId(userId);

    if (!premiumStatus) {
      premiumStatus = UserPremiumStatus.create(userId);
    }

    premiumStatus.processDonation(donation);

    await this.userPremiumRepository.save(premiumStatus);

    // Publish domain events
    const events = premiumStatus.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Re-evaluate user's premium status (after refund, etc.)
   */
  private async reevaluatePremiumStatus(userId: string): Promise<void> {
    const premiumStatus = await this.userPremiumRepository.findByUserId(userId);
    const totalDonated = await this.donationRepository.getTotalDonatedByUser(userId);

    if (!premiumStatus) {
      return;
    }

    const kofiTier = DonationTier.createKofiSupporter();

    // If user no longer qualifies for tier, revoke it
    if (!totalDonated.isGreaterThanOrEqual(kofiTier.monthlyAmount)) {
      premiumStatus.revokePremiumStatus('No longer qualifies for tier');

      await this.userPremiumRepository.save(premiumStatus);

      // Publish domain events
      const events = premiumStatus.getDomainEvents();
      for (const event of events) {
        await this.eventBus.publish(event);
      }
    }
  }

  /**
   * Validate Ko-fi webhook payload
   */
  private validateKofiPayload(payload: KofiDonationPayload): void {
    if (!payload.kofi_transaction_id) {
      throw new BusinessRuleViolationError('Ko-fi transaction ID is required');
    }

    if (!payload.amount || isNaN(parseFloat(payload.amount))) {
      throw new BusinessRuleViolationError('Valid amount is required');
    }

    if (parseFloat(payload.amount) <= 0) {
      throw new BusinessRuleViolationError('Amount must be greater than zero');
    }

    if (!payload.currency) {
      throw new BusinessRuleViolationError('Currency is required');
    }

    if (!payload.from_name) {
      throw new BusinessRuleViolationError('Donor name is required');
    }
  }

  /**
   * Get tier by name (could be moved to a TierRegistry)
   */
  private getTierByName(tierName: string): DonationTier {
    switch (tierName) {
      case 'Ko-fi Supporter':
        return DonationTier.createKofiSupporter();
      default:
        throw new BusinessRuleViolationError(`Unknown tier: ${tierName}`);
    }
  }

  /**
   * Create a DonationTier from a tier number
   */
  private createTierFromNumber(tierNumber: number): DonationTier {
    switch (tierNumber) {
      case 1:
        return DonationTier.create(
          'tier-1',
          'Bronze Supporter',
          Money.create(5.00, 'USD'),
          ['Premium badge', 'Basic perks']
        );
      case 2:
        return DonationTier.create(
          'tier-2',
          'Silver Supporter',
          Money.create(10.00, 'USD'),
          ['Premium badge', 'Priority support', 'Custom hub names']
        );
      case 3:
        return DonationTier.create(
          'tier-3',
          'Gold Supporter',
          Money.create(25.00, 'USD'),
          ['Premium badge', 'Priority support', 'Custom hub names', 'Early access']
        );
      case 4:
        return DonationTier.create(
          'tier-4',
          'Platinum Supporter',
          Money.create(50.00, 'USD'),
          ['Premium badge', 'Priority support', 'Custom hub names', 'Early access', 'VIP perks']
        );
      default:
        return DonationTier.createKofiSupporter(); // Default to basic supporter
    }
  }

  /**
   * Calculate premium eligibility for a donation
   */
  async calculatePremiumEligibility(
    userId: string,
    amount: Money
  ): Promise<{ eligible: boolean; tier: number; durationMonths: number }> {
    // Get user's current donation total
    const totalDonated = await this.getUserTotalDonated(userId);
    const newTotal = totalDonated.add(amount);

    // Determine tier based on total donation amount
    const tier = this.calculateTierFromAmount(newTotal);

    // Calculate additional duration based on this donation
    const durationMonths = this.calculateDurationFromAmount(amount);

    return {
      eligible: tier > 0 && durationMonths > 0,
      tier,
      durationMonths
    };
  }

  /**
   * Grant or extend premium for a user
   */
  async grantOrExtendPremium(
    userId: string,
    tier: number,
    durationMonths: number,
    existingPremium?: UserPremiumStatus
  ): Promise<UserPremiumStatus> {
    const now = new Date();
    let expiresAt: Date | null = null;

    if (durationMonths > 0) {
      // Calculate new expiration date
      const baseDate = existingPremium && existingPremium.expiresAt && existingPremium.expiresAt > now
        ? existingPremium.expiresAt  // Extend from current expiration
        : now;  // Start from now if no existing premium or expired

      expiresAt = new Date(baseDate);
      expiresAt.setMonth(expiresAt.getMonth() + durationMonths);
    }

    // Create donation tier from tier number
    const donationTier = this.createTierFromNumber(tier);

    if (existingPremium) {
      // Update existing premium
      existingPremium.grantPremiumTier(donationTier, expiresAt);
      return existingPremium;
    } else {
      // Create new premium status
      const newPremium = UserPremiumStatus.create(userId);
      newPremium.grantPremiumTier(donationTier, expiresAt);
      return newPremium;
    }
  }

  /**
   * Calculate tier from total donation amount
   */
  private calculateTierFromAmount(totalAmount: Money): number {
    // Convert to USD for consistent tier calculation
    const usdAmount = this.convertToUSD(totalAmount);

    if (usdAmount >= 50) return 4;  // Tier 4: $50+
    if (usdAmount >= 25) return 3;  // Tier 3: $25+
    if (usdAmount >= 10) return 2;  // Tier 2: $10+
    if (usdAmount >= 5) return 1;   // Tier 1: $5+

    return 0; // No tier
  }

  /**
   * Calculate duration from individual donation amount
   */
  private calculateDurationFromAmount(amount: Money): number {
    // Convert to USD for consistent calculation
    const usdAmount = this.convertToUSD(amount);

    // 1 month per $5 donated, minimum 1 month for any donation $5+
    return Math.max(Math.floor(usdAmount / 5), usdAmount >= 5 ? 1 : 0);
  }

  /**
   * Convert money to USD (simplified - in reality you'd use exchange rates)
   */
  private convertToUSD(money: Money): number {
    // Simplified conversion rates (in reality, use a proper exchange rate service)
    const rates: Record<string, number> = {
      'USD': 1.0,
      'EUR': 1.1,
      'GBP': 1.25,
      'CAD': 0.75,
      'AUD': 0.65,
    };

    const rate = rates[money.currency] || 1.0;
    return money.amount * rate;
  }
}
