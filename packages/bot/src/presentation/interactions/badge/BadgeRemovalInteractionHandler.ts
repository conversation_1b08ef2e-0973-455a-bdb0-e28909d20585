/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Badge Removal Interaction Handler
 *
 * Handles button interactions for badge removal confirmation.
 * Demonstrates Discord components integration with the new architecture.
 */

import { injectable, inject } from 'inversify';
import { ButtonInteraction, EmbedBuilder } from 'discord.js';
import { BaseInteractionHandler } from '../BaseInteractionHandler.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { IUserRepository } from '../../../domain/repositories/UserRepositories.js';
import type { Context } from '../../../shared/context/Context.js';

/**
 * Badge Removal Interaction Handler
 *
 * Demonstrates how Discord components integrate with the new command architecture.
 */
@injectable()
export class BadgeRemovalInteractionHandler extends BaseInteractionHandler {
  readonly handlerId = 'badge_remove';

  constructor(
    @inject(TYPES.UserRepository)
    private readonly userRepository: IUserRepository
  ) {
    super();
  }

  async handleButton(context: Context, interaction: ButtonInteraction): Promise<void> {
    // Parse custom ID to get action and parameters
    const customIdParts = interaction.customId.split('_');
    
    if (customIdParts.length < 4) {
      await interaction.reply({
        content: '❌ Invalid button interaction.',
        ephemeral: true
      });
      return;
    }

    const action = customIdParts[2]; // 'confirm' or 'cancel'
    const targetUserId = customIdParts[3];
    const badgeType = customIdParts[4];

    if (action === 'confirm') {
      await this.handleConfirmRemoval(interaction, targetUserId, badgeType);
    } else if (action === 'cancel') {
      await this.handleCancelRemoval(interaction);
    } else {
      await interaction.reply({
        content: '❌ Unknown action.',
        ephemeral: true
      });
    }
  }

  /**
   * Handle badge removal confirmation
   */
  private async handleConfirmRemoval(
    interaction: ButtonInteraction,
    targetUserId: string,
    badgeType: string
  ): Promise<void> {
    try {
      // Get target user info
      const targetUser = await interaction.client.users.fetch(targetUserId);
      
      if (!targetUser) {
        await interaction.reply({
          content: '❌ Could not find the target user.',
          ephemeral: true
        });
        return;
      }

      // Check if user exists in our system
      const user = await this.userRepository.findById(targetUserId);

      if (!user) {
        await interaction.reply({
          content: '❌ User not found in the InterChat system.',
          ephemeral: true
        });
        return;
      }

      // In a real implementation, you would:
      // 1. Check if user has this badge
      // 2. Remove the badge from the user's badge collection
      // 3. Log the action for audit purposes

      // Create success embed
      const embed = new EmbedBuilder()
        .setColor('#00FF00')
        .setTitle('Badge Removed Successfully')
        .setDescription(
          `✅ Successfully removed **${this.getBadgeName(badgeType)}** badge from ${targetUser.username}\n\n` +
          `**Moderator:** ${interaction.user.username}\n` +
          `**Target:** ${targetUser.username} (${targetUserId})\n` +
          `**Action:** Badge removal confirmed`
        )
        .setTimestamp();

      // Update the original message to show success
      await interaction.update({
        embeds: [embed],
        components: [] // Remove buttons after action
      });

    } catch (error) {
      console.error('Error removing badge:', error);
      
      await interaction.reply({
        content: '❌ An error occurred while removing the badge.',
        ephemeral: true
      });
    }
  }

  /**
   * Handle badge removal cancellation
   */
  private async handleCancelRemoval(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setColor('#FF0000')
      .setTitle('Badge Removal Cancelled')
      .setDescription('❌ Badge removal has been cancelled.')
      .setTimestamp();

    // Update the original message to show cancellation
    await interaction.update({
      embeds: [embed],
      components: [] // Remove buttons after action
    });
  }

  /**
   * Get display name for badge type
   */
  private getBadgeName(badgeType: string): string {
    const badgeNames: Record<string, string> = {
      'CHAMPION': '🏆 Champion',
      'ARTIST': '🎨 Artist',
      'DEVELOPER': '🛠️ Developer',
      'SUPPORTER': '🎭 Supporter',
      'VIP': '⭐ VIP',
      'BETA_TESTER': '🔧 Beta Tester'
    };

    return badgeNames[badgeType] || badgeType;
  }
}
