/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import {
  ButtonInteraction,
  ModalSubmitInteraction,
  StringSelectMenuInteraction,
  UserSelectMenuInteraction,
  RoleSelectMenuInteraction,
  ChannelSelectMenuInteraction,
  MentionableSelectMenuInteraction,
  MessageComponentInteraction,
  ModalSubmitInteraction as DiscordModalSubmitInteraction,
  Collection,
} from 'discord.js';
import type { BaseInteractionHandler } from './BaseInteractionHandler.js';
import type { Context } from '../../shared/context/Context.js';
import type { IEventBus } from '../../infrastructure/events/EventBus.js';
import { TYPES } from '../../shared/types/TYPES.js';

/**
 * Registry for managing interaction handlers
 * Routes interactions to the appropriate handler based on custom ID
 */
@injectable()
export class InteractionRegistry {
  private readonly handlers = new Collection<string, BaseInteractionHandler>();

  constructor(@inject(TYPES.EventBus) private readonly _eventBus: IEventBus) {}

  /**
   * Register an interaction handler
   */
  register(handler: BaseInteractionHandler): void {
    this.handlers.set(handler.handlerId, handler);
  }

  /**
   * Handle a button interaction
   */
  async handleButton(context: Context, interaction: ButtonInteraction): Promise<void> {
    const handler = this.findHandler(interaction.customId);
    if (!handler?.handleButton) {
      throw new Error(`No button handler found for customId: ${interaction.customId}`);
    }

    await handler.handleButton(context, interaction);
  }

  /**
   * Handle a modal submit interaction
   */
  async handleModal(context: Context, interaction: ModalSubmitInteraction): Promise<void> {
    const handler = this.findHandler(interaction.customId);
    if (!handler?.handleModal) {
      throw new Error(`No modal handler found for customId: ${interaction.customId}`);
    }

    await handler.handleModal(context, interaction);
  }

  /**
   * Handle a string select menu interaction
   */
  async handleStringSelect(
    context: Context,
    interaction: StringSelectMenuInteraction,
  ): Promise<void> {
    const handler = this.findHandler(interaction.customId);
    if (!handler?.handleStringSelect) {
      throw new Error(`No string select handler found for customId: ${interaction.customId}`);
    }

    await handler.handleStringSelect(context, interaction);
  }

  /**
   * Handle a user select menu interaction
   */
  async handleUserSelect(context: Context, interaction: UserSelectMenuInteraction): Promise<void> {
    const handler = this.findHandler(interaction.customId);
    if (!handler?.handleUserSelect) {
      throw new Error(`No user select handler found for customId: ${interaction.customId}`);
    }

    await handler.handleUserSelect(context, interaction);
  }

  /**
   * Handle a role select menu interaction
   */
  async handleRoleSelect(context: Context, interaction: RoleSelectMenuInteraction): Promise<void> {
    const handler = this.findHandler(interaction.customId);
    if (!handler?.handleRoleSelect) {
      throw new Error(`No role select handler found for customId: ${interaction.customId}`);
    }

    await handler.handleRoleSelect(context, interaction);
  }

  /**
   * Handle a channel select menu interaction
   */
  async handleChannelSelect(
    context: Context,
    interaction: ChannelSelectMenuInteraction,
  ): Promise<void> {
    const handler = this.findHandler(interaction.customId);
    if (!handler?.handleChannelSelect) {
      throw new Error(`No channel select handler found for customId: ${interaction.customId}`);
    }

    await handler.handleChannelSelect(context, interaction);
  }

  /**
   * Handle a mentionable select menu interaction
   */
  async handleMentionableSelect(
    context: Context,
    interaction: MentionableSelectMenuInteraction,
  ): Promise<void> {
    const handler = this.findHandler(interaction.customId);
    if (!handler?.handleMentionableSelect) {
      throw new Error(`No mentionable select handler found for customId: ${interaction.customId}`);
    }

    await handler.handleMentionableSelect(context, interaction);
  }

  /**
   * Route any interaction to the appropriate handler
   */
  async routeInteraction(
    context: Context,
    interaction: MessageComponentInteraction | DiscordModalSubmitInteraction,
  ): Promise<void> {
    if (interaction.isButton()) {
      await this.handleButton(context, interaction);
    } else if (interaction.isModalSubmit()) {
      await this.handleModal(context, interaction);
    } else if (interaction.isStringSelectMenu()) {
      await this.handleStringSelect(context, interaction);
    } else if (interaction.isUserSelectMenu()) {
      await this.handleUserSelect(context, interaction);
    } else if (interaction.isRoleSelectMenu()) {
      await this.handleRoleSelect(context, interaction);
    } else if (interaction.isChannelSelectMenu()) {
      await this.handleChannelSelect(context, interaction);
    } else if (interaction.isMentionableSelectMenu()) {
      await this.handleMentionableSelect(context, interaction);
    } else {
      throw new Error(`Unsupported interaction type: ${interaction.type}`);
    }
  }

  /**
   * Find the appropriate handler for a given custom ID
   */
  private findHandler(customId: string): BaseInteractionHandler | undefined {
    // First try exact match
    if (this.handlers.has(customId)) {
      return this.handlers.get(customId);
    }

    // Then try prefix match
    for (const entry of this.handlers.entries()) {
      const [_handlerId, handler] = entry;
      if (handler.canHandle(customId)) {
        return handler;
      }
    }

    return undefined;
  }

  /**
   * Get all registered handlers
   */
  getHandlers(): ReadonlyMap<string, BaseInteractionHandler> {
    return this.handlers;
  }
}
