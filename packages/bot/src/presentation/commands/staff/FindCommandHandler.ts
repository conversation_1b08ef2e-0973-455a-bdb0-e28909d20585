/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Find Command Handler
 *
 * Handles finding users and servers for staff members.
 */

import { injectable, inject } from 'inversify';
import {
  SlashCommandBuilder,
  type ChatInputCommandInteraction,
  EmbedBuilder
} from 'discord.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { IUserRepository } from '../../../domain/repositories/UserRepositories.js';
import type { Context } from '../../../shared/context/Context.js';

/**
 * Find Command Handler
 *
 * Provides search functionality for users and servers.
 */
@injectable()
export default class Find<PERSON>ommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'find',
    description: 'Find a user or server (Staff Only)',
    category: CommandCategory.STAFF,
    staffOnly: true,
    ownerOnly: false,
    guildOnly: false,
    cooldown: 3000,
  };

  constructor(
    @inject(TYPES.UserRepository)
    private readonly userRepository: IUserRepository
  ) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addSubcommand(subcommand =>
        subcommand
          .setName('user')
          .setDescription('Find a user by ID or username')
          .addStringOption(option =>
            option
              .setName('query')
              .setDescription('User ID or username to search for')
              .setRequired(true)
          )
      )
      .addSubcommand(subcommand =>
        subcommand
          .setName('server')
          .setDescription('Find a server by ID')
          .addStringOption(option =>
            option
              .setName('server_id')
              .setDescription('Server ID to search for')
              .setRequired(true)
          )
      );
  }

  async execute(ctx: Context,,): Promise<CommandResult> {
    try {
      const subcommand = interaction.options.getSubcommand();

      if (subcommand === 'user') {
        return await this.findUser(interaction);
      } else if (subcommand === 'server') {
        return await this.findServer(interaction, context);
      }

      const embed = this.createErrorEmbed(
        'Invalid Subcommand',
        'Please use either `user` or `server` subcommand.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };

    } catch (error) {
      const embed = this.createErrorEmbed(
        'Find Command Failed',
        'An error occurred while processing the find command.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  private async findUser(interaction: ChatInputCommandInteraction): Promise<CommandResult> {
    const query = interaction.options.getString('query', true);

    try {
      let user = null;

      // Check if query is a user ID (numeric string)
      if (/^\d{17,19}$/.test(query)) {
        user = await this.userRepository.findById(query);
      } else {
        // Search by username
        const users = await this.userRepository.searchByName(query, 1);
        user = users[0] || null;
      }

      if (!user) {
        const embed = this.createErrorEmbed(
          'User Not Found',
          `No user found matching: **${query}**`
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // Create user info embed
      const embed = new EmbedBuilder()
        .setTitle('👤 User Found')
        .setColor(0x00FF00)
        .addFields(
          { name: 'User ID', value: user.id, inline: true },
          { name: 'Username', value: user.name || 'Unknown', inline: true },
          { name: 'Staff', value: user.isStaff ? 'Yes' : 'No', inline: true },
          { name: 'Banned', value: user.isBanned ? 'Yes' : 'No', inline: true },
          { name: 'Messages', value: user.activity.messageCount.toString(), inline: true },
          { name: 'Reputation', value: user.activity.reputation.toString(), inline: true },
          { name: 'Created', value: `<t:${Math.floor(user.createdAt.getTime() / 1000)}:F>`, inline: false }
        )
        .setTimestamp();

      if (user.image) {
        embed.setThumbnail(user.image);
      }

      if (user.isBanned && user.banInfo.reason) {
        embed.addFields({ name: 'Ban Reason', value: user.banInfo.reason, inline: false });
      }

      return {
        success: true,
        embed,
        ephemeral: true,
      };

    } catch (error) {
      const embed = this.createErrorEmbed(
        'User Search Failed',
        'An error occurred while searching for the user.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  private async findServer(interaction: ChatInputCommandInteraction, context: Context): Promise<CommandResult> {
    const serverId = interaction.options.getString('server_id', true);

    // Validate server ID format
    if (!/^\d{17,19}$/.test(serverId)) {
      const embed = this.createErrorEmbed(
        'Invalid Server ID',
        'Please provide a valid Discord server ID.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }

    try {
      // Try to find the guild
      let guild = context.client.guilds.cache.get(serverId);

      // If not found locally, try to fetch it
      if (!guild) {
        try {
          guild = await context.client.guilds.fetch(serverId);
        } catch {
          // Guild not found or bot not in guild
        }
      }

      if (!guild) {
        const embed = this.createErrorEmbed(
          'Server Not Found',
          `No server found with ID: **${serverId}**\n\nThe bot may not be in this server.`
        );

        return {
          success: false,
          embed,
          ephemeral: true,
        };
      }

      // Create server info embed
      const embed = new EmbedBuilder()
        .setTitle('🌐 Server Found')
        .setColor(0x00FF00)
        .addFields(
          { name: 'Server ID', value: guild.id, inline: true },
          { name: 'Name', value: guild.name, inline: true },
          { name: 'Members', value: guild.memberCount?.toString() || 'Unknown', inline: true },
          { name: 'Owner', value: `<@${guild.ownerId}>`, inline: true },
          { name: 'Created', value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:F>`, inline: false },
          { name: 'Joined', value: guild.joinedTimestamp ? `<t:${Math.floor(guild.joinedTimestamp / 1000)}:F>` : 'Unknown', inline: false }
        )
        .setTimestamp();

      if (guild.iconURL()) {
        embed.setThumbnail(guild.iconURL()!);
      }

      return {
        success: true,
        embed,
        ephemeral: true,
      };

    } catch (error) {
      const embed = this.createErrorEmbed(
        'Server Search Failed',
        'An error occurred while searching for the server.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }
}
