/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Badge Command Handler
 *
 * Handles user badge management functionality for staff members.
 */

import { SlashCommandBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import type { IUserRepository } from '../../../domain/repositories/UserRepositories.js';
import type { Context } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { BaseCommandHandler, CommandCategory, type FlexibleCommandResponse } from '../BaseCommandHandler.js';
import { AddBadgeSubcommand } from './badge/AddBadgeSubcommand.js';
import { RemoveBadgeSubcommand } from './badge/RemoveBadgeSubcommand.js';

/**
 * Badge Command Handler
 *
 * Provides badge management functionality for staff members.
 */
@injectable()
export default class BadgeCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'badge',
    description: 'Manage user badges',
    category: CommandCategory.STAFF,
    staffOnly: true,
    ownerOnly: false,
    guildOnly: false,
    cooldown: 3000,
  };

  constructor(
    @inject(TYPES.UserRepository)
    private readonly userRepository: IUserRepository,
  ) {
    super();

    // Register subcommand handlers
    this.registerSubcommand(new AddBadgeSubcommand(this.userRepository));
    this.registerSubcommand(new RemoveBadgeSubcommand(this.userRepository));
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addSubcommand((subcommand) =>
        subcommand
          .setName('add')
          .setDescription('Add a badge to a user')
          .addUserOption((option) =>
            option.setName('user').setDescription('The user to add the badge to').setRequired(true),
          )
          .addStringOption((option) =>
            option
              .setName('badge')
              .setDescription('The badge to add')
              .setRequired(true)
              .addChoices(
                { name: '🏆 Champion', value: 'CHAMPION' },
                { name: '🎨 Artist', value: 'ARTIST' },
                { name: '🛠️ Developer', value: 'DEVELOPER' },
                { name: '🎭 Supporter', value: 'SUPPORTER' },
                { name: '⭐ VIP', value: 'VIP' },
                { name: '🔧 Beta Tester', value: 'BETA_TESTER' },
              ),
          ),
      )
      .addSubcommand((subcommand) =>
        subcommand
          .setName('remove')
          .setDescription('Remove a badge from a user')
          .addUserOption((option) =>
            option
              .setName('user')
              .setDescription('The user to remove the badge from')
              .setRequired(true),
          )
          .addStringOption((option) =>
            option
              .setName('badge')
              .setDescription('The badge to remove')
              .setRequired(true)
              .addChoices(
                { name: '🏆 Champion', value: 'CHAMPION' },
                { name: '🎨 Artist', value: 'ARTIST' },
                { name: '🛠️ Developer', value: 'DEVELOPER' },
                { name: '🎭 Supporter', value: 'SUPPORTER' },
                { name: '⭐ VIP', value: 'VIP' },
                { name: '🔧 Beta Tester', value: 'BETA_TESTER' },
              ),
          ),
      );
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    try {
      const subcommand = ctx.options.getSubcommand();

      if (!subcommand) {
        return {
          success: false,
          embed: this.createErrorEmbed(
            'No Subcommand',
            'Please specify a subcommand (add or remove).'
          ),
          ephemeral: true
        };
      }

      // Use the new subcommand system
      return await this.executeSubcommand(subcommand, ctx);

    } catch (error) {
      console.error('Error in badge command:', error);

      return {
        success: false,
        embed: this.createErrorEmbed(
          'Badge Command Failed',
          'An error occurred while processing the badge command.'
        ),
        ephemeral: true
      };
    }
  }

  // Old methods removed - now using subcommand file structure
  // See AddBadgeSubcommand.ts and RemoveBadgeSubcommand.ts for implementation
}
