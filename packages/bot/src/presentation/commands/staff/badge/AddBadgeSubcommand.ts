/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Add Badge Subcommand Handler
 *
 * <PERSON>les adding badges to users - demonstrates the new subcommand file structure.
 */

import { injectable, inject } from 'inversify';
import { ChatInputCommandInteraction } from 'discord.js';
import { BaseSubcommandHandler, FlexibleCommandResponse } from '../../BaseCommandHandler.js';
import { TYPES } from '../../../../shared/types/TYPES.js';
import type { IUserRepository } from '../../../../domain/repositories/UserRepositories.js';
import type { Context } from '../../../../shared/context/Context.js';

/**
 * Add Badge Subcommand Handler
 *
 * Demonstrates the new subcommand file structure with flexible response patterns.
 */
@injectable()
export class AddBadgeSubcommand extends BaseSubcommandHandler {
  readonly name = 'add';
  readonly description = 'Add a badge to a user';

  constructor(
    @inject(TYPES.UserRepository)
    private readonly userRepository: IUserRepository
  ) {
    super();
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    const interaction = ctx.interaction as ChatInputCommandInteraction;
    
    // Example of DIRECT response pattern - command handles response directly
    if (this.shouldUseDirect()) {
      await interaction.reply({
        content: '✅ Badge added successfully! (Direct response pattern)',
        ephemeral: true
      });
      return; // Return void - registry won't process further
    }

    // Example of STRUCTURED response pattern - return CommandResult
    const targetUser = interaction.options.getUser('user', true);
    const badgeType = interaction.options.getString('badge', true);

    try {
      // Check if user exists in our system
      const user = await this.userRepository.findById(targetUser.id);

      if (!user) {
        return {
          success: false,
          embed: this.createErrorEmbed(
            'User Not Found',
            'This user is not registered in the InterChat system.'
          ),
          ephemeral: true
        };
      }

      // In a real implementation, you would:
      // 1. Check if user already has this badge
      // 2. Add the badge to the user's badge collection
      // 3. Log the action for audit purposes

      // For now, we'll create a success message
      const embed = this.createSuccessEmbed(
        'Badge Added Successfully',
        `✅ Added **${this.getBadgeName(badgeType)}** badge to ${targetUser.username}\n\n` +
        `**Moderator:** ${interaction.user.username}\n` +
        `**Target:** ${targetUser.username} (${targetUser.id})`
      );

      return {
        success: true,
        embed,
        ephemeral: true
      };

    } catch (error) {
      console.error('Error adding badge:', error);
      
      return {
        success: false,
        embed: this.createErrorEmbed(
          'Failed to Add Badge',
          'An error occurred while adding the badge to the user.'
        ),
        ephemeral: true
      };
    }
  }

  /**
   * Example method to determine response pattern
   */
  private shouldUseDirect(): boolean {
    // In practice, this could be based on configuration, feature flags, etc.
    return false;
  }

  /**
   * Get display name for badge type
   */
  private getBadgeName(badgeType: string): string {
    const badgeNames: Record<string, string> = {
      'CHAMPION': '🏆 Champion',
      'ARTIST': '🎨 Artist',
      'DEVELOPER': '🛠️ Developer',
      'SUPPORTER': '🎭 Supporter',
      'VIP': '⭐ VIP',
      'BETA_TESTER': '🔧 Beta Tester'
    };

    return badgeNames[badgeType] || badgeType;
  }
}
