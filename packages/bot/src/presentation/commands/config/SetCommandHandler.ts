/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Set Command Handler
 *
 * Handles user preference configuration (language, reply mentions, etc.).
 */

import { SlashCommandBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import type { UpdateUserUseCase } from '../../../application/use-cases/users/UpdateUserUseCase.js';
import type { Context, SupportedLocaleCodes } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';

/**
 * Set Command Handler
 *
 * Allows users to configure their preferences.
 */
@injectable()
export default class SetCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'set',
    description: 'Set your preferences',
    category: CommandCategory.CONFIG,
    staffOnly: false,
    ownerOnly: false,
    guildOnly: false,
    cooldown: 3000,
  };

  constructor(
    @inject(TYPES.UpdateUserUseCase)
    private readonly updateUserUseCase: UpdateUserUseCase
  ) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addSubcommand(subcommand =>
        subcommand
          .setName('language')
          .setDescription('🈂️ Set the language in which I should respond to you')
          .addStringOption(option =>
            option
              .setName('lang')
              .setDescription('The language to set')
              .setRequired(true)
              .addChoices(
                { name: '🇺🇸 English', value: 'en' },
                { name: '🇪🇸 Español', value: 'es' },
                { name: '🇵🇹 Português', value: 'pt' },
                { name: '🇨🇳 中文', value: 'zh' },
                { name: '🇷🇺 Русский', value: 'ru' },
                { name: '🇮🇳 हिन्दी', value: 'hi' },
                { name: '🇪🇪 Eesti', value: 'et' },
                { name: '🇸🇦 العربية', value: 'ar' },
                { name: '🇧🇬 Български', value: 'bg' }
              )
          )
      )
      .addSubcommand(subcommand =>
        subcommand
          .setName('reply_mentions')
          .setDescription('🔔 Set whether you want to be mentioned when someone replies to your message')
          .addBooleanOption(option =>
            option
              .setName('enabled')
              .setDescription('Whether to enable reply mentions')
              .setRequired(true)
          )
      );
  }

  async execute(ctx: Context,): Promise<CommandResult> {
    try {
      const subcommand = ctx.options.getSubcommand();

      if (subcommand === 'language') {
        return await this.setLanguage(ctx);
      } else if (subcommand === 'reply_mentions') {
        return await this.setReplyMentions(ctx);
      }

      const embed = this.createErrorEmbed(
        'Invalid Subcommand',
        'Please use either `language` or `reply_mentions` subcommand.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };

    } catch (error) {
      const embed = this.createErrorEmbed(
        'Set Command Failed',
        'An error occurred while processing the set command.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  private async setLanguage(ctx: Context): Promise<CommandResult> {
    try {
      const locale = ctx.options.getString('lang', true) as SupportedLocaleCodes;

      // Update user language preference
      const result = await this.updateUserUseCase.execute({
        userId: ctx.user.id,
        name: ctx.user.username,
        image: ctx.user.displayAvatarURL(),
        locale,
      });

      const languageNames: Record<string, string> = {
        'en': '🇺🇸 English',
        'es': '🇪🇸 Español',
        'pt': '🇵🇹 Português',
        'zh': '🇨🇳 中文',
        'ru': '🇷🇺 Русский',
        'hi': '🇮🇳 हिन्दी',
        'et': '🇪🇪 Eesti',
        'ar': '🇸🇦 العربية',
        'bg': '🇧🇬 Български',
      };

      const languageName = languageNames[locale] || locale;

      if (result.updated) {
        const embed = this.createSuccessEmbed(
          'Language Updated',
          `✅ Your language has been set to **${languageName}**`
        );

        return {
          success: true,
          embed,
          ephemeral: true,
        };
      } else {
        const embed = this.createInfoEmbed(
          'No Changes Made',
          `Your language is already set to **${languageName}**.`
        );

        return {
          success: true,
          embed,
          ephemeral: true,
        };
      }

    } catch (error) {
      const embed = this.createErrorEmbed(
        'Failed to Update Language',
        'An error occurred while updating your language preference.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }

  private async setReplyMentions(ctx: Context): Promise<CommandResult> {
    try {
      const enabled = ctx.options.getBoolean('enabled', true) ?? false;

      // Update user reply mention preference
      const result = await this.updateUserUseCase.execute({
        userId: ctx.user.id,
        name: ctx.user.username,
        image: ctx.user.displayAvatarURL(),
        mentionOnReply: enabled,
      });

      if (result.updated) {
        const embed = this.createSuccessEmbed(
          'Reply Mentions Updated',
          enabled
            ? '✅ You will now be mentioned when someone replies to your messages'
            : '✅ You will no longer be mentioned when someone replies to your messages'
        );

        return {
          success: true,
          embed,
          ephemeral: true,
        };
      } else {
        const embed = this.createInfoEmbed(
          'No Changes Made',
          'Your reply mention preference is already set to this value.'
        );

        return {
          success: true,
          embed,
          ephemeral: true,
        };
      }

    } catch (error) {
      const embed = this.createErrorEmbed(
        'Failed to Update Reply Mentions',
        'An error occurred while updating your reply mention preference.'
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }
}
