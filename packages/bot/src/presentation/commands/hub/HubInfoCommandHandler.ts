/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { SlashCommandBuilder, SlashCommandOptionsOnlyBuilder, EmbedBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';
import type { Context } from '../../../shared/context/index.js';
import type { GetHubUseCase } from '../../../application/use-cases/hub/GetHubUseCase.js';
import { TYPES } from '../../../shared/types/TYPES.js';

@injectable()
export default class HubInfoCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hub-info',
    description: '📊 Show detailed information about a hub',
    category: CommandCategory.HUB,
    cooldown: 5, // 5 second cooldown
    permissions: [],
    guildOnly: false,
    ownerOnly: false,
  };

  constructor(
    @inject(TYPES.GetHubUseCase) private getHubUseCase: GetHubUseCase,
  ) {
    super();
  }

  buildCommand(): SlashCommandBuilder | SlashCommandOptionsOnlyBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption(option =>
        option
          .setName('name')
          .setDescription('The name of the hub to get information about')
          .setRequired(true)
      );
  }

  async execute(ctx: Context): Promise<CommandResult> {
    const hubName = ctx.options.getString('name', true);

    if (!hubName) {
      return {
        success: false,
        message: '❌ **Hub name is required.**',
        ephemeral: true,
      };
    }

    // Defer the reply since this operation might take a moment
    await ctx.deferReply({ flags: ['Ephemeral'] });

    try {
      const result = await this.getHubUseCase.execute({
        hubName,
        userId: ctx.user.id,
      });

      if (!result.success || !result.hub) {
        return {
          success: false,
          message: `❌ **Hub not found or you don't have access to it.**\n\n${result.error || 'Hub does not exist or is private'}`,
          ephemeral: true,
        };
      }

      const hub = result.hub;
      const isOwner = hub.isOwner(ctx.user.id);

      // Create the main embed
      const embed = new EmbedBuilder()
        .setTitle(`📊 ${hub.name}`)
        .setDescription(hub.description)
        .setColor(hub.isPrivate ? 0xff9500 : 0x0099ff);

      // Add icon if available
      if (hub.iconUrl) {
        embed.setThumbnail(hub.iconUrl);
      }

      // Add banner if available
      if (hub.bannerUrl) {
        embed.setImage(hub.bannerUrl);
      }

      // Add basic information
      embed.addFields(
        {
          name: '👑 Owner',
          value: `<@${hub.ownerId}>`,
          inline: true
        },
        {
          name: '🔒 Privacy',
          value: hub.isPrivate ? 'Private' : 'Public',
          inline: true
        },
        {
          name: '🔞 NSFW',
          value: hub.isNsfw ? 'Yes' : 'No',
          inline: true
        },
        {
          name: '📅 Created',
          value: `<t:${Math.floor(hub.createdAt.getTime() / 1000)}:F>`,
          inline: true
        },
        {
          name: '🕐 Last Active',
          value: `<t:${Math.floor(hub.lastActive.getTime() / 1000)}:R>`,
          inline: true
        },
        {
          name: '⏰ Appeal Cooldown',
          value: `${hub.appealCooldownHours} hours`,
          inline: true
        }
      );

      // Add welcome message if available and user has access
      if (hub.welcomeMessage && (isOwner || !hub.isPrivate)) {
        embed.addFields({
          name: '💬 Welcome Message',
          value: hub.welcomeMessage.length > 500
            ? hub.welcomeMessage.substring(0, 497) + '...'
            : hub.welcomeMessage,
          inline: false,
        });
      }

      // Add rules if available and user has access
      if (hub.rules.length > 0 && (isOwner || !hub.isPrivate)) {
        const rulesText = hub.rules
          .slice(0, 5) // Show only first 5 rules
          .map((rule, index) => `${index + 1}. ${rule}`)
          .join('\n');

        embed.addFields({
          name: `📋 Rules ${hub.rules.length > 5 ? `(showing 1-5 of ${hub.rules.length})` : ''}`,
          value: rulesText.length > 500
            ? rulesText.substring(0, 497) + '...'
            : rulesText,
          inline: false,
        });
      }

      // Add owner-only information
      if (isOwner) {
        embed.addFields({
          name: '🔧 Owner Settings',
          value: `Locked: ${hub.isLocked ? 'Yes' : 'No'}\n` +
                 `Settings: \`${hub.settings}\``,
          inline: false,
        });
      }

      // Set footer
      embed.setFooter({
        text: isOwner
          ? 'You own this hub'
          : hub.isPrivate
            ? 'This is a private hub'
            : 'This is a public hub'
      });

      return {
        success: true,
        embed,
        ephemeral: true,
      };
    } catch (error) {
      console.error('Error in hub info command:', error);
      return {
        success: false,
        message: '❌ **An unexpected error occurred while getting hub information.** Please try again later.',
        ephemeral: true,
      };
    }
  }
}
