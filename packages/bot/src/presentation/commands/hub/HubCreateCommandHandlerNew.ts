/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { SlashCommandBuilder, SlashCommandOptionsOnlyBuilder, EmbedBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';
import type { Context } from '../../../shared/context/index.js';
import type { CreateHubUseCase } from '../../../application/use-cases/hub/CreateHubUseCase.js';
import { TYPES } from '../../../shared/types/TYPES.js';

@injectable()
export default class HubCreateCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hub-create',
    description: '📝 Create a new hub for connecting servers',
    category: CommandCategory.HUB,
    cooldown: 30, // 30 second cooldown to prevent spam
    permissions: [],
    guildOnly: false,
    ownerOnly: false,
  };

  constructor(
    @inject(TYPES.CreateHubUseCase) private createHubUseCase: CreateHubUseCase,
  ) {
    super();
  }

  buildCommand(): SlashCommandBuilder | SlashCommandOptionsOnlyBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption(option =>
        option
          .setName('name')
          .setDescription('The name of the hub (3-32 characters)')
          .setRequired(true)
          .setMinLength(3)
          .setMaxLength(32)
      )
      .addStringOption(option =>
        option
          .setName('description')
          .setDescription('The description of the hub (max 500 characters)')
          .setRequired(true)
          .setMaxLength(500)
      )
      .addStringOption(option =>
        option
          .setName('icon-url')
          .setDescription('The icon URL for the hub (optional)')
          .setRequired(false)
      )
      .addStringOption(option =>
        option
          .setName('banner-url')
          .setDescription('The banner URL for the hub (optional)')
          .setRequired(false)
      );
  }

  async execute(ctx: Context): Promise<CommandResult> {
    const name = ctx.options.getString('name', true);
    const description = ctx.options.getString('description', true);
    const iconUrl = ctx.options.getString('icon-url');
    const bannerUrl = ctx.options.getString('banner-url');

    if (!name || !description) {
      return {
        success: false,
        message: '❌ **Name and description are required to create a hub.**',
        ephemeral: true,
      };
    }

    // Defer the reply since hub creation might take a moment
    await ctx.deferReply({ flags: ['Ephemeral'] });

    try {
      const result = await this.createHubUseCase.execute({
        name,
        description,
        ownerId: ctx.user.id,
        ...(iconUrl && { iconUrl }),
        ...(bannerUrl && { bannerUrl }),
      });

      if (result.success && result.hub) {
        const embed = new EmbedBuilder()
          .setColor(0x00ff00)
          .setTitle('✅ Hub Created Successfully!')
          .addFields(
            { name: 'Name', value: result.hub.name, inline: true },
            { name: 'Description', value: result.hub.description, inline: false },
            { name: 'Private', value: result.hub.isPrivate ? 'Yes' : 'No', inline: true },
            { name: 'Created', value: `<t:${Math.floor(result.hub.createdAt.getTime() / 1000)}:F>`, inline: true }
          )
          .setFooter({ text: 'Use /hub-invite to create invites for your new hub!' });

        if (result.hub.iconUrl) {
          embed.setThumbnail(result.hub.iconUrl);
        }

        return {
          success: true,
          embed,
          ephemeral: true,
        };
      } else {
        return {
          success: false,
          message: `❌ **Failed to create hub**\n\n${result.error || 'An unknown error occurred'}`,
          ephemeral: true,
        };
      }
    } catch (error) {
      console.error('Error in hub create command:', error);
      return {
        success: false,
        message: '❌ **An unexpected error occurred while creating the hub.** Please try again later.',
        ephemeral: true,
      };
    }
  }
}
