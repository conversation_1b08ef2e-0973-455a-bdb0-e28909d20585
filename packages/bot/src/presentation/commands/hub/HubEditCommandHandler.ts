/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { SlashCommandBuilder, SlashCommandOptionsOnlyBuilder, EmbedBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';
import type { Context } from '../../../shared/context/index.js';
import type { UpdateHubUseCase } from '../../../application/use-cases/hub/UpdateHubUseCase.js';
import type { GetHubUseCase } from '../../../application/use-cases/hub/GetHubUseCase.js';
import { TYPES } from '../../../shared/types/TYPES.js';

@injectable()
export default class HubEditCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hub-edit',
    description: '✏️ Edit your hub settings',
    category: CommandCategory.HUB,
    cooldown: 15, // 15 second cooldown
    permissions: [],
    guildOnly: false,
    ownerOnly: false,
  };

  constructor(
    @inject(TYPES.UpdateHubUseCase) private updateHubUseCase: UpdateHubUseCase,
    @inject(TYPES.GetHubUseCase) private getHubUseCase: GetHubUseCase,
  ) {
    super();
  }

  buildCommand(): SlashCommandBuilder | SlashCommandOptionsOnlyBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption(option =>
        option
          .setName('name')
          .setDescription('The name of the hub to edit')
          .setRequired(true)
      )
      .addStringOption(option =>
        option
          .setName('description')
          .setDescription('New description for the hub (max 500 characters)')
          .setRequired(false)
          .setMaxLength(500)
      )
      .addStringOption(option =>
        option
          .setName('icon-url')
          .setDescription('New icon URL for the hub')
          .setRequired(false)
      )
      .addStringOption(option =>
        option
          .setName('banner-url')
          .setDescription('New banner URL for the hub')
          .setRequired(false)
      )
      .addStringOption(option =>
        option
          .setName('welcome-message')
          .setDescription('New welcome message for the hub (max 1000 characters)')
          .setRequired(false)
          .setMaxLength(1000)
      )
      .addBooleanOption(option =>
        option
          .setName('private')
          .setDescription('Whether the hub should be private')
          .setRequired(false)
      )
      .addBooleanOption(option =>
        option
          .setName('nsfw')
          .setDescription('Whether the hub allows NSFW content')
          .setRequired(false)
      )
      .addIntegerOption(option =>
        option
          .setName('appeal-cooldown')
          .setDescription('Appeal cooldown in hours (1-168)')
          .setRequired(false)
          .setMinValue(1)
          .setMaxValue(168)
      );
  }

  async execute(ctx: Context): Promise<CommandResult> {
    const hubName = ctx.options.getString('name', true);
    const description = ctx.options.getString('description');
    const iconUrl = ctx.options.getString('icon-url');
    const bannerUrl = ctx.options.getString('banner-url');
    const welcomeMessage = ctx.options.getString('welcome-message');
    const isPrivate = ctx.options.getBoolean('private');
    const nsfw = ctx.options.getBoolean('nsfw');
    const appealCooldown = ctx.options.getInteger('appeal-cooldown');

    if (!hubName) {
      return {
        success: false,
        message: '❌ **Hub name is required.**',
        ephemeral: true,
      };
    }

    // Check if at least one field to update is provided
    const hasUpdates = description !== null || iconUrl !== null || bannerUrl !== null ||
                      welcomeMessage !== null || isPrivate !== null || nsfw !== null ||
                      appealCooldown !== null;

    if (!hasUpdates) {
      return {
        success: false,
        message: '❌ **Please provide at least one field to update.**',
        ephemeral: true,
      };
    }

    // Defer the reply since this operation might take a moment
    await ctx.deferReply({ flags: ['Ephemeral'] });

    try {
      // First, get the hub to validate ownership and existence
      const getResult = await this.getHubUseCase.execute({
        hubName,
        userId: ctx.user.id,
      });

      if (!getResult.success || !getResult.hub) {
        return {
          success: false,
          message: `❌ **Hub not found or you don't have access to it.**\n\n${getResult.error || 'Hub does not exist'}`,
          ephemeral: true,
        };
      }

      const hub = getResult.hub;

      // Check if user is the owner
      if (!hub.isOwner(ctx.user.id)) {
        return {
          success: false,
          message: '❌ **Only the hub owner can edit the hub.**',
          ephemeral: true,
        };
      }

      // Build update data object
      const updateData: any = {};
      if (description !== null) updateData.description = description;
      if (iconUrl !== null) updateData.iconUrl = iconUrl;
      if (bannerUrl !== null) updateData.bannerUrl = bannerUrl;
      if (welcomeMessage !== null) updateData.welcomeMessage = welcomeMessage;
      if (isPrivate !== null) updateData.private = isPrivate;
      if (nsfw !== null) updateData.nsfw = nsfw;
      if (appealCooldown !== null) updateData.appealCooldownHours = appealCooldown;

      // Attempt to update the hub
      const updateResult = await this.updateHubUseCase.execute({
        hubId: hub.id,
        userId: ctx.user.id,
        updateData,
      });

      if (updateResult.success && updateResult.hub) {
        const updatedHub = updateResult.hub;
        const changedFields: string[] = [];

        if (description !== null) changedFields.push('Description');
        if (iconUrl !== null) changedFields.push('Icon URL');
        if (bannerUrl !== null) changedFields.push('Banner URL');
        if (welcomeMessage !== null) changedFields.push('Welcome Message');
        if (isPrivate !== null) changedFields.push('Privacy');
        if (nsfw !== null) changedFields.push('NSFW Setting');
        if (appealCooldown !== null) changedFields.push('Appeal Cooldown');

        const embed = new EmbedBuilder()
          .setColor(0x00ff00)
          .setTitle('✏️ Hub Updated Successfully!')
          .setDescription(`The hub **${updatedHub.name}** has been updated.`)
          .addFields(
            { name: 'Updated Fields', value: changedFields.join(', '), inline: false },
            { name: 'Private', value: updatedHub.isPrivate ? 'Yes' : 'No', inline: true },
            { name: 'NSFW', value: updatedHub.isNsfw ? 'Yes' : 'No', inline: true },
            { name: 'Updated', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
          );

        if (updatedHub.iconUrl) {
          embed.setThumbnail(updatedHub.iconUrl);
        }

        return {
          success: true,
          embed,
          ephemeral: true,
        };
      } else {
        return {
          success: false,
          message: `❌ **Failed to update hub**\n\n${updateResult.error || 'An unknown error occurred'}`,
          ephemeral: true,
        };
      }
    } catch (error) {
      console.error('Error in hub edit command:', error);
      return {
        success: false,
        message: '❌ **An unexpected error occurred while updating the hub.** Please try again later.',
        ephemeral: true,
      };
    }
  }
}
