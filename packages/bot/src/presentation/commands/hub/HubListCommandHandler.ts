/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { SlashCommandBuilder, SlashCommandOptionsOnlyBuilder, EmbedBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';
import type { Context } from '../../../shared/context/index.js';
import type { ListHubsUseCase } from '../../../application/use-cases/hub/ListHubsUseCase.js';
import { TYPES } from '../../../shared/types/TYPES.js';

@injectable()
export default class HubListCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hub-list',
    description: '📋 List hubs',
    category: CommandCategory.HUB,
    cooldown: 10, // 10 second cooldown
    permissions: [],
    guildOnly: false,
    ownerOnly: false,
  };

  constructor(
    @inject(TYPES.ListHubsUseCase) private listHubsUseCase: ListHubsUseCase,
  ) {
    super();
  }

  buildCommand(): SlashCommandBuilder | SlashCommandOptionsOnlyBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption(option =>
        option
          .setName('type')
          .setDescription('What type of hubs to list')
          .setRequired(false)
          .addChoices(
            { name: 'My Hubs', value: 'owned' },
            { name: 'Public Hubs', value: 'public' },
            { name: 'Popular Hubs', value: 'popular' },
            { name: 'Search by Name', value: 'search' }
          )
      )
      .addStringOption(option =>
        option
          .setName('search')
          .setDescription('Search for hubs by name (only used with "Search by Name" type)')
          .setRequired(false)
          .setMinLength(3)
          .setMaxLength(32)
      )
      .addIntegerOption(option =>
        option
          .setName('limit')
          .setDescription('Maximum number of hubs to show (1-20)')
          .setRequired(false)
          .setMinValue(1)
          .setMaxValue(20)
      );
  }

  async execute(ctx: Context): Promise<CommandResult> {
    const type = ctx.options.getString('type') || 'popular';
    const search = ctx.options.getString('search');
    const limit = ctx.options.getInteger('limit') || 10;

    // Validate search parameter
    if (type === 'search' && !search) {
      return {
        success: false,
        message: '❌ **Search term is required when using "Search by Name" type.**',
        ephemeral: true,
      };
    }

    // Defer the reply since this operation might take a moment
    await ctx.deferReply({ flags: ['Ephemeral'] });

    try {
      let request: any = { limit };

      switch (type) {
        case 'owned':
          request.userId = ctx.user.id;
          request.includePrivate = true;
          break;
        case 'public':
          request.includePrivate = false;
          break;
        case 'popular':
          request.onlyPopular = true;
          break;
        case 'search':
          request.searchPattern = search;
          request.includePrivate = false;
          break;
        default:
          request.onlyPopular = true;
      }

      const result = await this.listHubsUseCase.execute(request);

      if (!result.success) {
        return {
          success: false,
          message: `❌ **Failed to list hubs**\n\n${result.error || 'An unknown error occurred'}`,
          ephemeral: true,
        };
      }

      const hubs = result.hubs || [];

      if (hubs.length === 0) {
        let message = '📋 **No hubs found.**';

        switch (type) {
          case 'owned':
            message = '📋 **You don\'t own any hubs yet.**\n\nUse `/hub-create` to create your first hub!';
            break;
          case 'search':
            message = `📋 **No hubs found matching "${search}".**`;
            break;
          case 'public':
            message = '📋 **No public hubs found.**';
            break;
          case 'popular':
            message = '📋 **No popular hubs found.**';
            break;
        }

        return {
          success: true,
          message,
          ephemeral: true,
        };
      }

      // Create the embed
      const embed = new EmbedBuilder()
        .setColor(0x0099ff)
        .setTitle('📋 Hub List')
        .setFooter({ text: `Showing ${hubs.length} hub${hubs.length === 1 ? '' : 's'}` });

      let description = '';
      switch (type) {
        case 'owned':
          description = 'Your hubs:';
          break;
        case 'public':
          description = 'Public hubs:';
          break;
        case 'popular':
          description = 'Popular hubs:';
          break;
        case 'search':
          description = `Search results for "${search}":`;
          break;
      }

      embed.setDescription(description);

      // Add hub fields
      for (let i = 0; i < Math.min(hubs.length, 10); i++) {
        const hub = hubs[i];
        const privacy = hub.isPrivate ? '🔒 Private' : '🌐 Public';
        const nsfw = hub.isNsfw ? ' 🔞' : '';

        let value = `${hub.description}\n${privacy}${nsfw}`;
        if (hub.isOwner(ctx.user.id)) {
          value += ' 👑';
        }

        embed.addFields({
          name: `${hub.name}`,
          value: value.length > 100 ? value.substring(0, 97) + '...' : value,
          inline: false,
        });
      }

      if (hubs.length > 10) {
        embed.addFields({
          name: 'Note',
          value: `Only showing first 10 results. ${hubs.length - 10} more hub${hubs.length - 10 === 1 ? '' : 's'} available.`,
          inline: false,
        });
      }

      return {
        success: true,
        embed,
        ephemeral: true,
      };
    } catch (error) {
      console.error('Error in hub list command:', error);
      return {
        success: false,
        message: '❌ **An unexpected error occurred while listing hubs.** Please try again later.',
        ephemeral: true,
      };
    }
  }
}
