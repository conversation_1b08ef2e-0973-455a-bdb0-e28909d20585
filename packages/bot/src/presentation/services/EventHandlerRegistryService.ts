/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import type { IEventBus, IEventHandler } from '../../infrastructure/events/TestInterfaces.js';
import { TYPES } from '../../shared/types/TYPES.js';

/**
 * Service that automatically registers all event handlers with the event bus
 */
@injectable()
export class EventHandlerRegistryService {
  constructor(
    @inject(TYPES.EventBus) private readonly eventBus: IEventBus,
    @inject(TYPES.DonationCreatedNotificationHandler) private readonly donationCreatedHandler: IEventHandler,
    @inject(TYPES.PremiumGrantedNotificationHandler) private readonly premiumGrantedHandler: IEventHandler,
    @inject(TYPES.PremiumExpiredNotificationHandler) private readonly premiumExpiredHandler: IEventHandler
  ) {}

  /**
   * Register all event handlers with the event bus
   */
  async initialize(): Promise<void> {
    console.log('🎯 Registering presentation layer event handlers...');

    // Register all handlers
    this.eventBus.subscribe('donation.created', this.donationCreatedHandler);
    this.eventBus.subscribe('premium.granted', this.premiumGrantedHandler);
    this.eventBus.subscribe('premium.expired', this.premiumExpiredHandler);

    console.log('✅ Event handlers registered successfully');
  }

  /**
   * Unregister all event handlers from the event bus
   */
  async dispose(): Promise<void> {
    console.log('🧹 Unregistering presentation layer event handlers...');

    // Unregister all handlers
    this.eventBus.unsubscribe('donation.created', this.donationCreatedHandler);
    this.eventBus.unsubscribe('premium.granted', this.premiumGrantedHandler);
    this.eventBus.unsubscribe('premium.expired', this.premiumExpiredHandler);

    console.log('✅ Event handlers unregistered successfully');
  }
}
