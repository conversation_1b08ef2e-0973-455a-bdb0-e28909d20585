/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable } from 'inversify';
import { DonationCreatedEvent } from '../../domain/events/DomainEvents.js';
import type { IEventHandler } from '../../infrastructure/events/TestInterfaces.js';

/**
 * Handles notification when a donation is created
 */
@injectable()
export class DonationCreatedNotificationHandler implements IEventHandler<DonationCreatedEvent> {
  readonly eventType = 'donation.created';
  readonly handlerId = 'DonationCreatedNotificationHandler';

  async handle(event: DonationCreatedEvent): Promise<void> {
    try {
      // Log the donation for debugging
      console.log(`[${this.handlerId}] Processing donation created event:`, {
        donationId: event.aggregateId,
        donorId: event.donorId,
        amount: event.amount,
        currency: event.currency,
        tier: event.tier,
        premiumGranted: event.premiumGranted,
        clusterId: event.clusterId,
        occurredAt: event.occurredAt,
      });

      // TODO: In a real implementation, this would:
      // 1. Send a thank you message to the user
      // 2. Notify administrators of the donation
      // 3. Update any live dashboards
      // 4. Send confirmation emails
      // 5. Update achievement progress

      // For now, we'll just log the event
      console.log(`✅ Donation ${event.aggregateId} created successfully for user ${event.donorId}`);
    } catch (error) {
      console.error(`❌ Error handling donation created event:`, error);
      throw error; // Re-throw to allow for retry mechanisms
    }
  }
}
