/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { initializeContainer } from '../infrastructure/di/Container.js';
import { TYPES } from '../shared/types/TYPES.js';
import { CommandRegistry } from '../presentation/commands/CommandRegistry.js';

/**
 * Test script for dynamic command loading
 */
async function testDynamicLoading() {
  console.log('🧪 Testing dynamic command loading...\n');

  try {
    // Initialize container with dynamic loading
    const container = await initializeContainer('test');

    // Get the command registry
    const commandRegistry = container.get<CommandRegistry>(TYPES.CommandRegistry);

    // Get all loaded commands
    const commands = commandRegistry.getAllCommands();

    console.log('\n📋 Loaded Commands:');
    console.log('==================');

    commands.forEach(cmd => {
      console.log(`• ${cmd.metadata.name} (${cmd.metadata.category})`);
      console.log(`  Description: ${cmd.metadata.description}`);
      console.log(`  Cooldown: ${cmd.metadata.cooldown || 'None'}`);
      console.log('');
    });

    console.log(`✅ Total commands loaded: ${commands.length}`);

    // Test command data generation
    const commandData = commandRegistry.getCommandData();
    console.log(`✅ Generated ${commandData.length} Discord command definitions`);

    // Get diagnostics
    const hybridHandler = container.get<any>(TYPES.HybridCommandHandler);
    const diagnostics = hybridHandler.getDiagnostics();

    console.log('\n🔍 System Diagnostics:');
    console.log('=====================');
    console.log(`New system available: ${diagnostics.newSystemAvailable}`);
    console.log(`Bridge status: ${diagnostics.bridgeStatus}`);
    console.log(`New commands: ${diagnostics.newCommands.join(', ')}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testDynamicLoading();
}
