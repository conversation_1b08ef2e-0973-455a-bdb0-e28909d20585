/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Container } from 'inversify';
import { join } from 'path';
import 'reflect-metadata';

// Import types
import { TYPES } from '../../shared/types/TYPES.js';

// Import infrastructure
import { Configuration } from '../config/Configuration.js';
import { ClusterEventBus, type IEventBus } from '../events/EventBus.js';

// Import repository implementations
import { DonationRepository } from '../database/repositories/DonationRepository.js';
import { HubRepository } from '../database/repositories/HubRepository.js';
import { UserPremiumRepository } from '../database/repositories/UserPremiumRepository.js';
import { UserRepository } from '../database/repositories/UserRepository.js';

// Import domain services
import { DonationDomainService } from '../../domain/services/DonationDomainService.js';
import { UserDomainService } from '../../domain/services/UserDomainService.js';

// Import application use cases
import { CreateDonationUseCase } from '../../application/use-cases/donations/CreateDonationUseCase.js';
import { GetUserPremiumUseCase } from '../../application/use-cases/donations/GetUserPremiumUseCase.js';
import { CreateHubUseCase } from '../../application/use-cases/hub/CreateHubUseCase.js';
import { DeleteHubUseCase } from '../../application/use-cases/hub/DeleteHubUseCase.js';
import { GetHubUseCase } from '../../application/use-cases/hub/GetHubUseCase.js';
import { ListHubsUseCase } from '../../application/use-cases/hub/ListHubsUseCase.js';
import { UpdateHubUseCase } from '../../application/use-cases/hub/UpdateHubUseCase.js';
import { CreateUserUseCase } from '../../application/use-cases/users/CreateUserUseCase.js';
import { GetUserUseCase } from '../../application/use-cases/users/GetUserUseCase.js';
import { UpdateUserUseCase } from '../../application/use-cases/users/UpdateUserUseCase.js';

// Import presentation services
import { EventHandlerRegistryService } from '../../presentation/services/EventHandlerRegistryService.js';

// Import presentation layer
import { CommandRegistry } from '../../presentation/commands/CommandRegistry.js';
import { InteractionRegistry } from '../../presentation/interactions/InteractionRegistry.js';

// Import infrastructure handlers and bridges
import { CommandBridge } from '../bridge/CommandBridge.js';
import { InteractionCreateEventHandler } from '../events/InteractionCreateEventHandler.js';
import { HybridCommandHandler } from '../handlers/HybridCommandHandler.js';

// Import event handlers
import {
    ClientReadyEventHandler,
    ClientShutdownEventHandler,
    GuildJoinedEventHandler,
    GuildLeftEventHandler,
} from '../../presentation/handlers/ClientEventHandlers.js';
import { DonationCreatedNotificationHandler } from '../../presentation/handlers/DonationCreatedNotificationHandler.js';
import { PremiumExpiredNotificationHandler } from '../../presentation/handlers/PremiumExpiredNotificationHandler.js';
import { PremiumGrantedNotificationHandler } from '../../presentation/handlers/PremiumGrantedNotificationHandler.js';

// Import dynamic loader
import { DynamicCommandLoader } from '../loaders/DynamicCommandLoader.js';
import { DynamicInteractionLoader } from '../loaders/DynamicInteractionLoader.js';

// Import external dependencies
import { Redis } from 'ioredis';
import { PrismaClient } from '../../../../../build/generated/prisma/client/index.js';
import { Logger } from '../../shared/utils/Logger.js';

/**
 * Cluster-Aware Dependency Injection Container
 *
 * Each cluster process creates its own container instance.
 * This ensures proper isolation between cluster processes while
 * allowing shared configuration and distributed events.
 */
export async function createClusterContainer(clusterId?: string): Promise<Container> {
    const container = new Container();

    // Bind the container itself (for services that need it)
    container.bind(TYPES.Container).toConstantValue(container);

    // === CONFIGURATION (Load first - others depend on it) ===
    container.bind(TYPES.Configuration).to(Configuration).inSingletonScope();

    // === INFRASTRUCTURE LAYER ===

    // Event Bus - Cluster-aware with Redis support for cross-cluster events
    container
        .bind<IEventBus>(TYPES.EventBus)
        .toDynamicValue(() => {
            const eventBus = new ClusterEventBus(clusterId || '0');

            // Inject Redis client for distributed events
            try {
                const redisClient = container.get<Redis>(TYPES.RedisClient);
                eventBus.setRedisClient(redisClient);
            } catch (error) {
                Logger.warn('⚠️ Redis not available for event bus, using local events only');
            }

            return eventBus;
        })
        .inSingletonScope();

    // Redis Client (for caching and distributed operations)
    container.bind(TYPES.RedisClient)
        .toDynamicValue(() => {
            const config = container.get<Configuration>(TYPES.Configuration);
            const redisConfig = config.get('redis');

            if (!redisConfig.url) {
                Logger.warn('⚠️ Redis URL not configured, some features may be limited');
                throw new Error('Redis not configured');
            }

            const redis = new Redis(redisConfig.url, {
                maxRetriesPerRequest: 3,
                lazyConnect: true,
                keepAlive: 30000,
                connectTimeout: 10000,
                family: 4, // IPv4
                db: 0,
                keyPrefix: redisConfig.keyPrefix,
            });

            redis.on('connect', () => Logger.info('✅ Redis connected'));
            redis.on('error', (error) => Logger.error('❌ Redis error:', error));

            return redis;
        })
        .inSingletonScope();

    // === REPOSITORIES (Data Access Layer) ===
    // Import repository implementations
    container.bind(TYPES.DonationRepository).to(DonationRepository).inSingletonScope();
    container.bind(TYPES.UserPremiumRepository).to(UserPremiumRepository).inSingletonScope();
    container.bind(TYPES.UserRepository).to(UserRepository).inSingletonScope();
    container.bind(TYPES.HubRepository).to(HubRepository).inSingletonScope();

    // === DOMAIN SERVICES ===
    // Bind the donation domain service
    container.bind(TYPES.DonationDomainService).to(DonationDomainService).inSingletonScope();
    // Bind the user domain service
    container.bind(TYPES.UserDomainService).to(UserDomainService).inSingletonScope();

    // Additional domain services will be added as they are migrated from the legacy system
    // container.bind(TYPES.PremiumService).to(PremiumService);

    // === APPLICATION SERVICES ===
    // Use Cases
    container.bind(TYPES.CreateDonationUseCase).to(CreateDonationUseCase).inSingletonScope();
    container.bind(TYPES.GetUserPremiumUseCase).to(GetUserPremiumUseCase).inSingletonScope();
    container.bind(TYPES.CreateUserUseCase).to(CreateUserUseCase).inSingletonScope();
    container.bind(TYPES.GetUserUseCase).to(GetUserUseCase).inSingletonScope();
    container.bind(TYPES.UpdateUserUseCase).to(UpdateUserUseCase).inSingletonScope();
    container.bind(TYPES.CreateHubUseCase).to(CreateHubUseCase).inSingletonScope();
    container.bind(TYPES.UpdateHubUseCase).to(UpdateHubUseCase).inSingletonScope();
    container.bind(TYPES.DeleteHubUseCase).to(DeleteHubUseCase).inSingletonScope();
    container.bind(TYPES.GetHubUseCase).to(GetHubUseCase).inSingletonScope();
    container.bind(TYPES.ListHubsUseCase).to(ListHubsUseCase).inSingletonScope();

    // === PRESENTATION LAYER ===
    // Command Registry (registers all command handlers)
    container.bind(TYPES.CommandRegistry).to(CommandRegistry).inSingletonScope();

    // Interaction Registry (registers all interaction handlers)
    container.bind(TYPES.InteractionRegistry).to(InteractionRegistry).inSingletonScope();

    // Event Handler Registry Service
    container
        .bind(TYPES.EventHandlerRegistryService)
        .to(EventHandlerRegistryService)
        .inSingletonScope();

    // === DOMAIN EVENT HANDLERS ===
    // Bind domain event handlers to the container
    container
        .bind(TYPES.DonationCreatedNotificationHandler)
        .to(DonationCreatedNotificationHandler)
        .inSingletonScope();
    container
        .bind(TYPES.PremiumGrantedNotificationHandler)
        .to(PremiumGrantedNotificationHandler)
        .inSingletonScope();
    container
        .bind(TYPES.PremiumExpiredNotificationHandler)
        .to(PremiumExpiredNotificationHandler)
        .inSingletonScope();

    // === EVENT HANDLERS ===
    // Bind event handlers to the container
    container.bind(ClientReadyEventHandler).toSelf().inSingletonScope();
    container.bind(ClientShutdownEventHandler).toSelf().inSingletonScope();
    container.bind(GuildJoinedEventHandler).toSelf().inSingletonScope();
    container.bind(GuildLeftEventHandler).toSelf().inSingletonScope();

    // === DYNAMIC LOADING ===
    // Command Loader
    container.bind(TYPES.DynamicCommandLoader).to(DynamicCommandLoader).inSingletonScope();

    // Interaction Loader
    container.bind(TYPES.DynamicInteractionLoader).to(DynamicInteractionLoader).inSingletonScope();

    // Load all command handlers dynamically from the file system
    Logger.info('🔄 Loading command handlers dynamically...');
    await DynamicCommandLoader.loadCommandHandlers(container);

    // Load all interaction handlers dynamically from the file system
    Logger.info('🔄 Loading interaction handlers dynamically...');
    const interactionLoader = container.get<DynamicInteractionLoader>(TYPES.DynamicInteractionLoader);
    await interactionLoader.loadInteractions(
        join(import.meta.dirname, '../../presentation/interactions'),
    );

    // === INFRASTRUCTURE HANDLERS ===
    // Bridge and hybrid handlers for command system integration
    container.bind(TYPES.CommandBridge).to(CommandBridge).inSingletonScope();
    container.bind(TYPES.HybridCommandHandler).to(HybridCommandHandler).inSingletonScope();
    container
        .bind(TYPES.InteractionCreateEventHandler)
        .to(InteractionCreateEventHandler)
        .inSingletonScope();

    // === EVENT HANDLERS ===
    // Client event handlers for domain events
    container.bind('ClientReadyEventHandler').to(ClientReadyEventHandler).inSingletonScope();
    container.bind('ClientShutdownEventHandler').to(ClientShutdownEventHandler).inSingletonScope();
    container.bind('GuildJoinedEventHandler').to(GuildJoinedEventHandler).inSingletonScope();
    container.bind('GuildLeftEventHandler').to(GuildLeftEventHandler).inSingletonScope();

    // Subscribe event handlers to the event bus
    const eventBus = container.get<IEventBus>(TYPES.EventBus);
    eventBus.subscribe('client.ready', container.get(ClientReadyEventHandler));
    eventBus.subscribe('client.shutdown', container.get(ClientShutdownEventHandler));
    eventBus.subscribe('guild.joined', container.get(GuildJoinedEventHandler));
    eventBus.subscribe('guild.left', container.get(GuildLeftEventHandler));

    // === EXTERNAL SERVICES ===
    // Discord Client is bound in ModernClient.ts after initialization

    // Prisma Client (Database client for data access)
    container.bind(TYPES.PrismaClient).toConstantValue(new PrismaClient());

    return container;
}

/**
 * Global container instance for the current cluster
 * This is set once when the cluster starts up
 */
let globalContainer: Container | null = null;

/**
 * Initialize the DI container for this cluster
 * This should be called once when the cluster process starts
 */
export async function initializeContainer(clusterId?: string): Promise<Container> {
    if (globalContainer) {
        throw new Error('Container already initialized for this cluster');
    }

    globalContainer = await createClusterContainer(clusterId);

    // Log cluster initialization
    const config = globalContainer.get<Configuration>(TYPES.Configuration);
    Logger.info(`✅ Cluster ${clusterId || '0'} container initialized`);
    Logger.info(`📊 Environment: ${config.getEnvironment()}`);
    Logger.info(`🔧 Development mode: ${config.isDevelopment()}`);

    return globalContainer;
}

/**
 * Get the global container for this cluster
 * Throws an error if the container hasn't been initialized
 */
export function getContainer(): Container {
    if (!globalContainer) {
        throw new Error('Container not initialized. Call initializeContainer() first.');
    }

    return globalContainer;
}

/**
 * Get a service from the container
 * Convenience method for getting services without importing the container
 */
export function getService<T>(serviceType: symbol): T {
    return getContainer().get<T>(serviceType);
}

/**
 * Cleanup and dispose of the container
 * This should be called when the cluster is shutting down
 */
export async function disposeContainer(): Promise<void> {
    if (globalContainer) {
        // Cleanup resources
        try {
            // Close Redis connection
            const redis = globalContainer.get<Redis>(TYPES.RedisClient);
            await redis.quit();
            Logger.info('✅ Redis connection closed');
        } catch (error) {
            Logger.debug('Redis cleanup skipped (not configured)');
        }

        try {
            // Close Prisma connection
            const prisma = globalContainer.get<PrismaClient>(TYPES.PrismaClient);
            await prisma.$disconnect();
            Logger.info('✅ Database connection closed');
        } catch (error) {
            Logger.error('❌ Error closing database connection:', error);
        }

        globalContainer = null;
        Logger.info('🧹 Container disposed');
    }
}

/**
 * Check if the container has been initialized
 */
export function isContainerInitialized(): boolean {
    return globalContainer !== null;
}

/**
 * Development helper to inspect container bindings
 */
export function inspectContainer(): void {
    if (!globalContainer) {
        Logger.info('❌ Container not initialized');
        return;
    }

    Logger.info('🔍 Container inspection:');
    Logger.info('📦 Container initialized');

    // Check key services
    try {
        globalContainer.get(TYPES.Configuration);
        Logger.info('✅ Configuration service available');
    } catch { Logger.warn('⚠️ Configuration service not available'); }

    try {
        globalContainer.get(TYPES.PrismaClient);
        Logger.info('✅ Database client available');
    } catch { Logger.warn('⚠️ Database client not available'); }

    try {
        globalContainer.get(TYPES.RedisClient);
        Logger.info('✅ Redis client available');
    } catch { Logger.warn('⚠️ Redis client not available'); }

    try {
        globalContainer.get(TYPES.EventBus);
        Logger.info('✅ Event bus available');
    } catch { Logger.warn('⚠️ Event bus not available'); }
}
