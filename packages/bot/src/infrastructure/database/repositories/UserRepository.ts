/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * User Repository Implementation
 *
 * Implements user persistence operations using Prisma.
 */

import { injectable, inject } from 'inversify';
import { PrismaClient, type HubActivityLevel } from '../../../../../../build/generated/prisma/client/index.js';
import type { IUserRepository } from '../../../domain/repositories/UserRepositories.js';
import { User } from '../../../domain/entities/User.js';
import { TYPES } from '../../../shared/types/TYPES.js';

@injectable()
export class UserRepository implements IUserRepository {
  constructor(
    @inject(TYPES.PrismaClient) private readonly prisma: PrismaClient
  ) {}

  async save(user: User): Promise<void> {
    const data = user.toPersistence();

    await this.prisma.user.upsert({
      where: { id: data.id },
      create: data,
      update: {
        name: data.name,
        image: data.image,
        locale: data.locale,
        showBadges: data.showBadges,
        mentionOnReply: data.mentionOnReply,
        voteCount: data.voteCount,
        reputation: data.reputation,
        lastVoted: data.lastVoted,
        banReason: data.banReason,
        messageCount: data.messageCount,
        lastMessageAt: data.lastMessageAt,
        inboxLastReadDate: data.inboxLastReadDate,
        updatedAt: data.updatedAt,
        isStaff: data.isStaff,
        email: data.email,
        emailVerified: data.emailVerified,
        donationEmail: data.donationEmail,
        preferredLanguages: data.preferredLanguages,
        activityLevel: data.activityLevel,
        showNsfwHubs: data.showNsfwHubs,
        donationExpiresAt: data.donationExpiresAt,
        hubJoinCount: data.hubJoinCount,
        lastHubJoinAt: data.lastHubJoinAt,
        hubEngagementScore: data.hubEngagementScore,
      },
    });
  }

  async findById(id: string): Promise<User | null> {
    const userData = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!userData) {
      return null;
    }

    return User.fromPersistence(userData);
  }

  async findByEmail(email: string): Promise<User | null> {
    const userData = await this.prisma.user.findFirst({
      where: { email },
    });

    if (!userData) {
      return null;
    }

    return User.fromPersistence(userData);
  }

  async findByStaffStatus(isStaff: boolean): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      where: { isStaff },
      orderBy: { createdAt: 'desc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async findBannedUsers(): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      where: {
        banReason: { not: null },
      },
      orderBy: { updatedAt: 'desc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async findByActivityLevel(activityLevel: HubActivityLevel): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      where: { activityLevel },
      orderBy: { hubEngagementScore: 'desc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async findByPreferredLanguages(languages: string[]): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      where: {
        preferredLanguages: {
          hasSome: languages,
        },
      },
      orderBy: { hubEngagementScore: 'desc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async findHighEngagementUsers(minScore: number, limit = 50): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      where: {
        hubEngagementScore: { gte: minScore },
      },
      orderBy: { hubEngagementScore: 'desc' },
      take: limit,
    });

    return users.map(user => User.fromPersistence(user));
  }

  async findRecentVoters(days: number): Promise<User[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const users = await this.prisma.user.findMany({
      where: {
        lastVoted: { gte: cutoffDate },
      },
      orderBy: { lastVoted: 'desc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async findByReputationRange(minReputation: number, maxReputation?: number): Promise<User[]> {
    const whereClause: any = {
      reputation: { gte: minReputation },
    };

    if (maxReputation !== undefined) {
      whereClause.reputation.lte = maxReputation;
    }

    const users = await this.prisma.user.findMany({
      where: whereClause,
      orderBy: { reputation: 'desc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async findRecentHubJoiners(days: number): Promise<User[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const users = await this.prisma.user.findMany({
      where: {
        lastHubJoinAt: { gte: cutoffDate },
      },
      orderBy: { lastHubJoinAt: 'desc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async searchByName(nameQuery: string, limit = 20): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      where: {
        name: {
          contains: nameQuery,
          mode: 'insensitive',
        },
      },
      orderBy: { reputation: 'desc' },
      take: limit,
    });

    return users.map(user => User.fromPersistence(user));
  }

  async getUserStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    staffUsers: number;
    bannedUsers: number;
    premiumUsers: number;
  }> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [totalUsers, activeUsers, staffUsers, bannedUsers, premiumUsers] = await Promise.all([
      this.prisma.user.count(),
      this.prisma.user.count({
        where: {
          lastMessageAt: { gte: thirtyDaysAgo },
        },
      }),
      this.prisma.user.count({
        where: { isStaff: true },
      }),
      this.prisma.user.count({
        where: { banReason: { not: null } },
      }),
      this.prisma.user.count({
        where: {
          donationExpiresAt: { gt: new Date() },
        },
      }),
    ]);

    return {
      totalUsers,
      activeUsers,
      staffUsers,
      bannedUsers,
      premiumUsers,
    };
  }

  async findUsersWithExpiringPremium(days: number): Promise<User[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    const users = await this.prisma.user.findMany({
      where: {
        donationExpiresAt: {
          lte: futureDate,
          gt: new Date(),
        },
      },
      orderBy: { donationExpiresAt: 'asc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async findByLocale(locale: string): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      where: { locale },
      orderBy: { createdAt: 'desc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async findInactiveUsers(days: number): Promise<User[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const users = await this.prisma.user.findMany({
      where: {
        lastMessageAt: { lt: cutoffDate },
      },
      orderBy: { lastMessageAt: 'asc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async getTopMessageSenders(limit = 10): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      orderBy: { messageCount: 'desc' },
      take: limit,
    });

    return users.map(user => User.fromPersistence(user));
  }

  async getTopReputationUsers(limit = 10): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      orderBy: { reputation: 'desc' },
      take: limit,
    });

    return users.map(user => User.fromPersistence(user));
  }

  async getTopVoters(limit = 10): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      orderBy: { voteCount: 'desc' },
      take: limit,
    });

    return users.map(user => User.fromPersistence(user));
  }

  async exists(id: string): Promise<boolean> {
    const count = await this.prisma.user.count({
      where: { id },
    });

    return count > 0;
  }

  async delete(id: string): Promise<void> {
    await this.prisma.user.delete({
      where: { id },
    });
  }

  async findByCreationDateRange(startDate: Date, endDate: Date): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async findByBadges(badgeIds: string[]): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      where: {
        badges: {
          some: {
            id: { in: badgeIds },
          },
        },
      },
      include: {
        badges: true,
      },
      orderBy: { reputation: 'desc' },
    });

    return users.map(user => User.fromPersistence(user));
  }

  async updateLastActivity(userId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        lastMessageAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }

  async bulkUpdate(updates: Array<{ userId: string; data: Partial<any> }>): Promise<void> {
    const updatePromises = updates.map(({ userId, data }) =>
      this.prisma.user.update({
        where: { id: userId },
        data: {
          ...data,
          updatedAt: new Date(),
        },
      })
    );

    await Promise.all(updatePromises);
  }

  async getUserCountByDateRange(startDate: Date, endDate: Date): Promise<number> {
    return await this.prisma.user.count({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    });
  }

  async findByPreferences(preferences: {
    showBadges?: boolean;
    mentionOnReply?: boolean;
    showNsfwHubs?: boolean;
  }): Promise<User[]> {
    const whereClause: any = {};

    if (preferences.showBadges !== undefined) {
      whereClause.showBadges = preferences.showBadges;
    }

    if (preferences.mentionOnReply !== undefined) {
      whereClause.mentionOnReply = preferences.mentionOnReply;
    }

    if (preferences.showNsfwHubs !== undefined) {
      whereClause.showNsfwHubs = preferences.showNsfwHubs;
    }

    const users = await this.prisma.user.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
    });

    return users.map(user => User.fromPersistence(user));
  }
}
