/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import { PrismaClient } from '../../../../../../build/generated/prisma/client/index.js';
import type { IHubRepository } from '../../../domain/repositories/HubRepositories.js';
import { Hub } from '../../../domain/entities/Hub.js';
import { TYPES } from '../../../shared/types/TYPES.js';

@injectable()
export class HubRepository implements IHubRepository {
  constructor(
    @inject(TYPES.PrismaClient) private readonly prisma: PrismaClient
  ) {}

  async save(hub: Hub): Promise<void> {
    const data = hub.toPersistence();

    if (data.id) {
      // Update existing hub
      await this.prisma.hub.update({
        where: { id: data.id },
        data: {
          name: data.name,
          description: data.description,
          iconUrl: data.iconUrl,
          bannerUrl: data.bannerUrl,
          welcomeMessage: data.welcomeMessage,
          private: data.private,
          locked: data.locked,
          nsfw: data.nsfw,
          appealCooldownHours: data.appealCooldownHours,
          settings: data.settings,
          rules: data.rules,
          lastActive: data.lastActive,
          updatedAt: data.updatedAt,
        },
      });
    } else {
      // Create new hub
      const created = await this.prisma.hub.create({
        data: {
          name: data.name,
          description: data.description,
          ownerId: data.ownerId,
          iconUrl: data.iconUrl,
          bannerUrl: data.bannerUrl,
          welcomeMessage: data.welcomeMessage,
          private: data.private,
          locked: data.locked,
          nsfw: data.nsfw,
          appealCooldownHours: data.appealCooldownHours,
          settings: data.settings,
          rules: data.rules,
          lastActive: data.lastActive,
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
        },
      });

      // Update the domain entity with the generated ID
      const updatedData = { ...data, id: created.id };
      Object.assign(hub, { data: updatedData });
    }
  }

  async findById(id: string): Promise<Hub | null> {
    const hubData = await this.prisma.hub.findUnique({
      where: { id },
    });

    return hubData ? Hub.fromPersistence(hubData) : null;
  }

  async findByName(name: string): Promise<Hub | null> {
    const hubData = await this.prisma.hub.findFirst({
      where: {
        name: {
          equals: name,
          mode: 'insensitive'
        }
      },
    });

    return hubData ? Hub.fromPersistence(hubData) : null;
  }

  async findByOwnerId(ownerId: string): Promise<Hub[]> {
    const hubsData = await this.prisma.hub.findMany({
      where: { ownerId },
      orderBy: { createdAt: 'desc' },
    });

    return hubsData.map(hub => Hub.fromPersistence(hub));
  }

  async findModeratedByUserId(userId: string, roles?: string[]): Promise<Hub[]> {
    // First get owned hubs
    const ownedHubs = await this.findByOwnerId(userId);

    // Then get moderated hubs
    const moderatedData = await this.prisma.hubModerator.findMany({
      where: {
        userId,
        ...(roles ? { role: { in: roles as any } } : {}),
      },
      include: {
        hub: true,
      },
    });

    const moderatedHubs = moderatedData.map(mod => Hub.fromPersistence(mod.hub));

    // Combine and deduplicate
    const allHubs = [...ownedHubs];
    for (const modHub of moderatedHubs) {
      if (!allHubs.find(h => h.id === modHub.id)) {
        allHubs.push(modHub);
      }
    }

    return allHubs;
  }

  async findByNamePattern(
    pattern: string,
    options: {
      caseSensitive?: boolean;
      searchType?: 'contains' | 'equals' | 'startsWith' | 'endsWith';
      isPrivate?: boolean;
      take?: number;
    } = {}
  ): Promise<Hub[]> {
    const {
      caseSensitive = false,
      searchType = 'contains',
      isPrivate,
      take = 25,
    } = options;

    let nameFilter: any;
    switch (searchType) {
      case 'equals':
        nameFilter = { equals: pattern };
        break;
      case 'startsWith':
        nameFilter = { startsWith: pattern };
        break;
      case 'endsWith':
        nameFilter = { endsWith: pattern };
        break;
      default:
        nameFilter = { contains: pattern };
    }

    if (!caseSensitive) {
      nameFilter.mode = 'insensitive';
    }

    const hubsData = await this.prisma.hub.findMany({
      where: {
        name: nameFilter,
        ...(isPrivate !== undefined ? { private: isPrivate } : {}),
      },
      take,
      orderBy: { lastActive: 'desc' },
    });

    return hubsData.map(hub => Hub.fromPersistence(hub));
  }

  async findPopular(limit = 5): Promise<Hub[]> {
    const hubsData = await this.prisma.hub.findMany({
      where: { private: false },
      include: {
        _count: {
          select: {
            connections: { where: { connected: true } },
          },
        },
      },
      orderBy: {
        connections: { _count: 'desc' },
      },
      take: limit,
    });

    return hubsData.map(hub => Hub.fromPersistence(hub));
  }

  async delete(hubId: string): Promise<void> {
    // Note: In a real implementation, you'd want to handle related data deletion
    // This is simplified for the entity demonstration
    await this.prisma.hub.delete({
      where: { id: hubId },
    });
  }

  async existsByName(name: string): Promise<boolean> {
    const count = await this.prisma.hub.count({
      where: {
        name: {
          equals: name,
          mode: 'insensitive'
        }
      },
    });

    return count > 0;
  }

  async countByOwnerId(ownerId: string): Promise<number> {
    return await this.prisma.hub.count({
      where: { ownerId },
    });
  }

  async findWithFilters(filters: {
    hasConnections?: boolean;
    minimumConnections?: number;
    lastActiveAfter?: Date;
    isPrivate?: boolean;
    skip?: number;
    take?: number;
  }): Promise<Hub[]> {
    const {
      hasConnections,
      minimumConnections,
      lastActiveAfter,
      isPrivate,
      skip = 0,
      take = 10,
    } = filters;

    const where: any = {};

    if (isPrivate !== undefined) {
      where.private = isPrivate;
    }

    if (lastActiveAfter) {
      where.lastActive = { gte: lastActiveAfter };
    }

    if (hasConnections) {
      where.connections = { some: { connected: true } };
    }

    if (minimumConnections !== undefined) {
      where.connections = {
        ...where.connections,
        _count: { gte: minimumConnections },
      };
    }

    const hubsData = await this.prisma.hub.findMany({
      where,
      skip,
      take,
      orderBy: { lastActive: 'desc' },
    });

    return hubsData.map(hub => Hub.fromPersistence(hub));
  }
}
