/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import { readdir, stat } from 'fs/promises';
import { join, extname } from 'path';
import { pathToFileURL } from 'url';
import type { Container } from 'inversify';
import type { BaseInteractionHandler } from '../../presentation/interactions/BaseInteractionHandler.js';
import type { InteractionRegistry } from '../../presentation/interactions/InteractionRegistry.js';
import { TYPES } from '../../shared/types/TYPES.js';
import { Logger } from '../../shared/utils/Logger.js';

/**
 * Dynamically loads and registers interaction handlers from the filesystem
 */
@injectable()
export class DynamicInteractionLoader {
  constructor(
    @inject(TYPES.Container) private readonly container: Container,
    @inject(TYPES.InteractionRegistry) private readonly interactionRegistry: InteractionRegistry,
  ) {}

  /**
   * Load all interaction handlers from the specified directory
   */
  async loadInteractions(basePath: string): Promise<void> {
    let loadedHandlers = 0;
    let scannedDirs = 0;

    try {
      // Dynamically discover all interaction directories
      const interactionDirs = await this.discoverInteractionDirectories(basePath);

      for (const dir of interactionDirs) {
        const dirPath = join(basePath, dir);
        try {
          const handlersInDir = await this.loadInteractionsFromDirectory(dirPath);
          loadedHandlers += handlersInDir;
          scannedDirs++;
        } catch (error) {
          Logger.error(`❌ Error loading interactions from ${dirPath}:`, error);
        }
      }
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        Logger.info(`📁 Interactions base directory '${basePath}' not found (migration in progress)`);
      } else {
        Logger.error(`❌ Error scanning interactions directory:`, error);
      }
    }

    Logger.info(
      `✅ Interaction system initialized: ${loadedHandlers} handlers from ${scannedDirs} directories`,
    );
  }

  /**
   * Dynamically discover all interaction directories in the base path
   */
  private async discoverInteractionDirectories(basePath: string): Promise<string[]> {
    const interactionDirs: string[] = [];

    try {
      const entries = await readdir(basePath);

      for (const entry of entries) {
        const entryPath = join(basePath, entry);
        const entryStat = await stat(entryPath);

        // Only include directories, skip files like BaseInteractionHandler.ts
        if (entryStat.isDirectory()) {
          interactionDirs.push(entry);
          Logger.info(`🔍 Discovered interaction directory: ${entry}`);
        }
      }
    } catch (error) {
      // If the directory doesn't exist, return empty array
      if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {
        Logger.error(`❌ Error discovering interaction directories:`, error);
      }
    }

    return interactionDirs;
  }

  /**
   * Load interaction handlers from a specific directory
   */
  private async loadInteractionsFromDirectory(directoryPath: string): Promise<number> {
    let loadedCount = 0;

    try {
      const files = await readdir(directoryPath);

      for (const file of files) {
        const filePath = join(directoryPath, file);
        const fileStat = await stat(filePath);

        if (fileStat.isDirectory()) {
          // Recursively load from subdirectories
          loadedCount += await this.loadInteractionsFromDirectory(filePath);
        } else if (this.isTypeScriptFile(file)) {
          const loaded = await this.loadInteractionFromFile(filePath);
          if (loaded) loadedCount++;
        }
      }
    } catch (error) {
      Logger.error(`❌ Error reading directory ${directoryPath}:`, error);
    }

    return loadedCount;
  }

  /**
   * Load a single interaction handler from a file
   */
  private async loadInteractionFromFile(filePath: string): Promise<boolean> {
    try {
      const fileUrl = pathToFileURL(filePath).href;
      const module = await import(fileUrl);

      // Look for the default export or a named export
      const HandlerClass = module.default || module.InteractionHandler;

      if (HandlerClass && this.isInteractionHandlerClass(HandlerClass)) {
        // Bind to container if not already bound
        if (!this.container.isBound(HandlerClass)) {
          this.container.bind(HandlerClass).toSelf();
        }

        // Get instance and register
        const handler = this.container.get<BaseInteractionHandler>(HandlerClass);
        this.interactionRegistry.register(handler);

        Logger.info(`✅ Loaded interaction handler: ${handler.handlerId} from ${filePath}`);
        return true;
      }
    } catch (error) {
      Logger.error(`❌ Error loading interaction handler from ${filePath}:`, error);
    }

    return false;
  }

  /**
   * Check if a file is a TypeScript or JavaScript file we should process
   */
  private isTypeScriptFile(filename: string): boolean {
    const ext = extname(filename);
    return (
      (ext === '.ts' || ext === '.js') &&
      !filename.endsWith('.test.ts') &&
      !filename.endsWith('.test.js') &&
      !filename.endsWith('.spec.ts') &&
      !filename.endsWith('.spec.js') &&
      !filename.startsWith('.')
    );
  }

  /**
   * Check if a class is an interaction handler
   */
  private isInteractionHandlerClass(constructor: any): boolean {
    if (!constructor || typeof constructor !== 'function') {
      return false;
    }

    try {
      // Check if it has the required properties
      const prototype = constructor.prototype;
      return prototype && 'handlerId' in prototype && 'canHandle' in prototype;
    } catch {
      return false;
    }
  }

  /**
   * Get diagnostic information about loaded interactions
   */
  getDiagnostics(): {
    totalHandlers: number;
    handlerIds: string[];
  } {
    const handlers = this.interactionRegistry.getHandlers();
    return {
      totalHandlers: handlers.size,
      handlerIds: Array.from(handlers.keys()),
    };
  }
}
