/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import type { Interaction, CacheType } from 'discord.js';
import { HybridCommandHandler } from '../handlers/HybridCommandHandler.js';
import type { InteractionRegistry } from '../../presentation/interactions/InteractionRegistry.js';
import { TYPES } from '../../shared/types/TYPES.js';

/**
 * Modern InteractionCreate Event Handler
 *
 * This event handler routes all Discord interactions through our modern system:
 * - Commands (slash/context) → HybridCommandHandler
 * - Buttons/Modals/Selects → InteractionRegistry
 */
@injectable()
export class InteractionCreateEventHandler {
  constructor(
    @inject(TYPES.HybridCommandHandler) private readonly hybridHandler: HybridCommandHandler,
    @inject(TYPES.InteractionRegistry) private readonly interactionRegistry: InteractionRegistry
  ) {}

  /**
   * Handle Discord interaction events
   */
  async execute(interaction: Interaction<CacheType>): Promise<void> {
    try {
      // Handle command interactions (slash commands, context menus, autocomplete)
      if (
        interaction.isChatInputCommand() ||
        interaction.isContextMenuCommand() ||
        interaction.isAutocomplete()
      ) {
        await this.hybridHandler.handleCommand(interaction);
        return;
      }

      // Handle component interactions (buttons, modals, select menus)
      if (
        interaction.isButton() ||
        interaction.isModalSubmit() ||
        interaction.isAnySelectMenu()
      ) {
        // For now, create a basic context wrapper
        // TODO: Create proper ComponentContext for non-command interactions
        const context = {
          interaction,
          guild: interaction.guild,
          user: interaction.user,
          member: interaction.member
        } as any;

        await this.interactionRegistry.routeInteraction(context, interaction);
        return;
      }

      // Log unhandled interaction types for debugging
      console.log(`⚠️ Unhandled interaction type: ${interaction.type}`);

    } catch (error) {
      console.error('Error in InteractionCreateEventHandler:', error);
    }
  }

  /**
   * Get diagnostic information
   */
  getDiagnostics() {
    return {
      handler: 'HybridCommandHandler',
      ...this.hybridHandler.getDiagnostics()
    };
  }
}
