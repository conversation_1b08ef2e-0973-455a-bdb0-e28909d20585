/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable } from 'inversify';
import type { ChatInputCommandInteraction, AutocompleteInteraction } from 'discord.js';
import { CommandRegistry } from '../../presentation/commands/CommandRegistry.js';
import { getContainer } from '../di/Container.js';
import { TYPES } from '../../shared/types/TYPES.js';

/**
 * Command Bridge
 *
 * Bridges the legacy command system with the new CommandRegistry system.
 * This allows for gradual migration of commands to the new architecture.
 */
@injectable()
export class CommandBridge {
  private commandRegistry: CommandRegistry | null = null;

  /**
   * Initialize the bridge with the command registry
   */
  private getCommandRegistry(): CommandRegistry {
    if (!this.commandRegistry) {
      try {
        const container = getContainer();
        this.commandRegistry = container.get<CommandRegistry>(TYPES.CommandRegistry);
      } catch (error) {
        console.warn('CommandRegistry not available:', error);
        throw new Error('CommandRegistry not initialized');
      }
    }
    return this.commandRegistry;
  }

  /**
   * Check if a command exists in the new registry
   */
  hasCommand(commandName: string): boolean {
    try {
      const registry = this.getCommandRegistry();
      return registry.getCommand(commandName) !== undefined;
    } catch {
      return false;
    }
  }

  /**
   * Execute a command using the new registry
   */
  async executeCommand(interaction: ChatInputCommandInteraction): Promise<boolean> {
    try {
      const registry = this.getCommandRegistry();
      const command = registry.getCommand(interaction.commandName);

      if (!command) {
        return false; // Command not found in new registry
      }

      await registry.executeCommand(interaction);
      return true; // Command executed successfully
    } catch (error) {
      console.error('Error executing command via CommandBridge:', error);
      return false; // Fall back to legacy system
    }
  }

  /**
   * Handle autocomplete using the new registry
   */
  async handleAutocomplete(interaction: AutocompleteInteraction): Promise<boolean> {
    try {
      const registry = this.getCommandRegistry();
      const command = registry.getCommand(interaction.commandName);

      if (!command) {
        return false; // Command not found in new registry
      }

      // TODO: Implement autocomplete handling in CommandRegistry
      console.warn('Autocomplete not yet implemented in new CommandRegistry');
      return false; // Fall back to legacy system for now
    } catch (error) {
      console.error('Error handling autocomplete via CommandBridge:', error);
      return false; // Fall back to legacy system
    }
  }

  /**
   * Get all available commands from the new registry
   */
  getNewCommands(): string[] {
    try {
      const registry = this.getCommandRegistry();
      return registry.getAllCommands().map(cmd => cmd.metadata.name);
    } catch {
      return [];
    }
  }

  /**
   * Check if the new command system is available
   */
  isAvailable(): boolean {
    try {
      this.getCommandRegistry();
      return true;
    } catch {
      return false;
    }
  }
}
