08/07/25-11:06:11 INFO: 🚀 Starting Modern InterChat Client...
08/07/25-11:06:11 ERROR: ❌ Failed to start Modern InterChat Client: A [Error]: No bindings found for service: "ClientReadyEventHandler".

Trying to resolve bindings for "ClientReadyEventHandler (Root service)".

Binding constraints:
- service identifier: ClientReadyEventHandler
- name: -
    at new A (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/error/models/InversifyCoreError.ts:15:5)
    at isOptional (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/planning/calculations/throwErrorWhenUnexpectedBindingsAmountFound.ts:62:3)
    at bindingConstraints (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/planning/calculations/throwErrorWhenUnexpectedBindingsAmountFound.ts:73:10)
    at bindingConstraints (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/planning/calculations/throwErrorWhenUnexpectedBindingsAmountFound.ts:77:9)
    at Me (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/planning/calculations/checkServiceNodeSingleInjectionBindings.ts:25:11)
    at planResult (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/planning/calculations/plan.ts:82:45)
    at this (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/container/src/container/services/ServiceResolutionManager.ts:236:11)
    at get (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/container/src/container/services/ServiceResolutionManager.ts:85:36)
    at createClusterContainer (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/di/Container.ts:163:47)
    at processTicksAndRejections (native) {
  kind: 2,
  Symbol(@inversifyjs/core/InversifyCoreError): true
}
08/07/25-11:10:39 INFO: 🚀 Starting Modern InterChat Client...
08/07/25-11:10:39 INFO: 🔄 Loading command handlers dynamically...
08/07/25-11:10:39 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-11:10:39 ERROR: ❌ Failed to start Modern InterChat Client: Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
    at new PrismaClient (/home/<USER>/Desktop/interchat/node_modules/.prisma/client/default.js:43:10)
    at createClusterContainer (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/di/Container.ts:182:53)
    at processTicksAndRejections (native)
08/07/25-11:13:11 INFO: 🚀 Starting Modern InterChat Client...
08/07/25-11:13:11 INFO: 🔄 Loading command handlers dynamically...
08/07/25-11:13:11 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-11:13:11 ERROR: ❌ Failed to start Modern InterChat Client: Error: Configuration validation failed:
bot.token: Required
redis.url: Required
    at loadAndValidateConfig (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:205:14)
    at new instance (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:117:23)
    at binding (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveInstanceBindingNodeFromConstructorParams.ts:28:11)
    at this (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveScoped.ts:34:13)
    at get (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/container/src/container/services/ServiceResolutionManager.ts:92:7)
    at initializeContainer (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/di/Container.ts:205:33)
    at processTicksAndRejections (native)
08/07/25-11:15:00 INFO: 🚀 Starting Modern InterChat Client...
08/07/25-11:15:00 INFO: 🔄 Loading command handlers dynamically...
08/07/25-11:15:00 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:15:00 INFO: ✅ Loaded 0 command handlers
08/07/25-11:15:00 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-11:15:00 ERROR: ❌ Failed to start Modern InterChat Client: Error: Configuration validation failed:
bot.token: Required
redis.url: Required
    at loadAndValidateConfig (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:205:14)
    at new instance (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:117:23)
    at binding (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveInstanceBindingNodeFromConstructorParams.ts:28:11)
    at this (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveScoped.ts:34:13)
    at get (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/container/src/container/services/ServiceResolutionManager.ts:92:7)
    at initializeContainer (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/di/Container.ts:205:33)
    at processTicksAndRejections (native)
08/07/25-11:15:57 INFO: 🚀 Starting Modern InterChat Client (Cluster unknown)...
08/07/25-11:15:57 INFO: 🚀 Starting Modern InterChat Client...
08/07/25-11:15:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-11:15:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:15:57 INFO:   ✓ Loaded: donation-create (DonationCreateCommandHandler)
08/07/25-11:15:57 INFO:   ✓ Loaded: premium-status (PremiumStatusCommandHandler)
08/07/25-11:15:57 INFO:   ✓ Loaded: stats (StatsCommandHandler)
08/07/25-11:15:57 INFO:   ✓ Loaded: ping (PingCommandHandler)
08/07/25-11:15:57 INFO:   ✓ Loaded: about (AboutCommandHandler)
08/07/25-11:15:57 INFO: ✅ Loaded 5 command handlers
08/07/25-11:15:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-11:15:57 ERROR: ❌ Failed to start Modern InterChat Client: Error: Configuration validation failed:
bot.token: Required
redis.url: Required
    at loadAndValidateConfig (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:205:14)
    at new instance (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:117:23)
    at binding (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveInstanceBindingNodeFromConstructorParams.ts:28:11)
    at this (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveScoped.ts:34:13)
    at get (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/container/src/container/services/ServiceResolutionManager.ts:92:7)
    at initializeContainer (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/di/Container.ts:205:33)
    at processTicksAndRejections (native)
08/07/25-11:15:57 ERROR: ❌ Failed to start client: Error: Configuration validation failed:
bot.token: Required
redis.url: Required
    at loadAndValidateConfig (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:205:14)
    at new instance (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:117:23)
    at binding (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveInstanceBindingNodeFromConstructorParams.ts:28:11)
    at this (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveScoped.ts:34:13)
    at get (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/container/src/container/services/ServiceResolutionManager.ts:92:7)
    at initializeContainer (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/di/Container.ts:205:33)
    at processTicksAndRejections (native)
08/07/25-11:18:38 INFO: 🚀 Starting Modern InterChat Client (Cluster unknown)...
08/07/25-11:18:38 INFO: 🚀 Starting Modern InterChat Client...
08/07/25-11:18:38 INFO: 🔄 Loading command handlers dynamically...
08/07/25-11:18:38 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:18:38 INFO:   ✓ Loaded: donation-create (DonationCreateCommandHandler)
08/07/25-11:18:38 INFO:   ✓ Loaded: premium-status (PremiumStatusCommandHandler)
08/07/25-11:18:38 INFO:   ✓ Loaded: stats (StatsCommandHandler)
08/07/25-11:18:38 INFO:   ✓ Loaded: ping (PingCommandHandler)
08/07/25-11:18:38 INFO:   ✓ Loaded: about (AboutCommandHandler)
08/07/25-11:18:38 INFO: ✅ Loaded 5 command handlers
08/07/25-11:18:38 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-11:18:38 ERROR: ❌ Failed to start Modern InterChat Client: Error: Configuration validation failed:
bot.token: Required
redis.url: Required
    at loadAndValidateConfig (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:205:14)
    at new instance (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:117:23)
    at binding (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveInstanceBindingNodeFromConstructorParams.ts:28:11)
    at this (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveScoped.ts:34:13)
    at get (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/container/src/container/services/ServiceResolutionManager.ts:92:7)
    at initializeContainer (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/di/Container.ts:205:33)
    at processTicksAndRejections (native)
08/07/25-11:18:38 ERROR: ❌ Failed to start client: Error: Configuration validation failed:
bot.token: Required
redis.url: Required
    at loadAndValidateConfig (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:205:14)
    at new instance (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:117:23)
    at binding (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveInstanceBindingNodeFromConstructorParams.ts:28:11)
    at this (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveScoped.ts:34:13)
    at get (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/container/src/container/services/ServiceResolutionManager.ts:92:7)
    at initializeContainer (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/di/Container.ts:205:33)
    at processTicksAndRejections (native)
08/07/25-11:19:12 INFO: 🚀 Starting Modern InterChat Client (Cluster unknown)...
08/07/25-11:19:12 INFO: 🚀 Starting Modern InterChat Client...
08/07/25-11:19:12 INFO: 🔄 Loading command handlers dynamically...
08/07/25-11:19:12 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:19:12 INFO:   ✓ Loaded: donation-create (DonationCreateCommandHandler)
08/07/25-11:19:12 INFO:   ✓ Loaded: premium-status (PremiumStatusCommandHandler)
08/07/25-11:19:12 INFO:   ✓ Loaded: stats (StatsCommandHandler)
08/07/25-11:19:12 INFO:   ✓ Loaded: ping (PingCommandHandler)
08/07/25-11:19:12 INFO:   ✓ Loaded: about (AboutCommandHandler)
08/07/25-11:19:12 INFO: ✅ Loaded 5 command handlers
08/07/25-11:19:12 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-11:19:12 ERROR: ❌ Failed to start Modern InterChat Client: Error: Configuration validation failed:
bot.token: Required
redis.url: Required
    at loadAndValidateConfig (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:205:14)
    at new instance (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:117:23)
    at binding (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveInstanceBindingNodeFromConstructorParams.ts:28:11)
    at this (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveScoped.ts:34:13)
    at get (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/container/src/container/services/ServiceResolutionManager.ts:92:7)
    at initializeContainer (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/di/Container.ts:205:33)
    at processTicksAndRejections (native)
08/07/25-11:19:12 ERROR: ❌ Failed to start client: Error: Configuration validation failed:
bot.token: Required
redis.url: Required
    at loadAndValidateConfig (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:205:14)
    at new instance (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/config/Configuration.ts:117:23)
    at binding (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveInstanceBindingNodeFromConstructorParams.ts:28:11)
    at this (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/core/src/resolution/actions/resolveScoped.ts:34:13)
    at get (/home/<USER>/Desktop/interchat/node_modules/@inversifyjs/container/src/container/services/ServiceResolutionManager.ts:92:7)
    at initializeContainer (/home/<USER>/Desktop/interchat/packages/bot/src/infrastructure/di/Container.ts:205:33)
    at processTicksAndRejections (native)
08/07/25-11:21:08 INFO: 🚀 Starting Modern InterChat Client (Cluster unknown)...
08/07/25-11:21:08 INFO: 🚀 Starting Modern InterChat Client...
08/07/25-11:21:08 INFO: 🔄 Loading command handlers dynamically...
08/07/25-11:21:08 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:21:08 INFO:   ✓ Loaded: donation-create (DonationCreateCommandHandler)
08/07/25-11:21:08 INFO:   ✓ Loaded: premium-status (PremiumStatusCommandHandler)
08/07/25-11:21:08 INFO:   ✓ Loaded: stats (StatsCommandHandler)
08/07/25-11:21:08 INFO:   ✓ Loaded: ping (PingCommandHandler)
08/07/25-11:21:08 INFO:   ✓ Loaded: about (AboutCommandHandler)
08/07/25-11:21:08 INFO: ✅ Loaded 5 command handlers
08/07/25-11:21:08 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-11:21:08 INFO: ✅ Cluster 0 container initialized
08/07/25-11:21:08 INFO: 📊 Environment: development
08/07/25-11:21:08 INFO: 🔧 Development mode: true
08/07/25-11:21:08 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:21:08 INFO:   ✓ Loaded: donation-create (DonationCreateCommandHandler)
08/07/25-11:21:08 INFO:   ✓ Loaded: premium-status (PremiumStatusCommandHandler)
08/07/25-11:21:08 INFO:   ✓ Loaded: stats (StatsCommandHandler)
08/07/25-11:21:08 INFO:   ✓ Loaded: ping (PingCommandHandler)
08/07/25-11:21:08 INFO:   ✓ Loaded: about (AboutCommandHandler)
08/07/25-11:21:08 INFO: ✅ Loaded 5 command handlers
08/07/25-11:21:08 INFO: ✅ Command system ready with 5 commands
08/07/25-11:21:08 INFO: 🔍 Debug: Provided token: Nzk4NzQ4MDE1NDM1MDU1MTM0.G0mkR8.**************************************
08/07/25-11:21:08 INFO: 🔍 Debug: Preparing to connect to the gateway...
08/07/25-11:21:08 INFO: 🌐 REST Debug: [REST Global(GET:/gateway/bot):global] Received bucket hash update
  Old Hash  : Global(GET:/gateway/bot)
  New Hash  : 41f9cd5d28af77da04563bcb1d67fdfd
08/07/25-11:21:08 INFO: 🔍 Debug: [WS => Manager] Fetched Gateway Information
	URL: wss://gateway.discord.gg
	Recommended Shards: 1
08/07/25-11:21:08 INFO: 🔍 Debug: [WS => Manager] Session Limit Information
	Total: 1000
	Remaining: 996
08/07/25-11:21:08 INFO: 🔍 Debug: [WS => Shard 0] Connecting to wss://gateway.discord.gg?v=10&encoding=json
08/07/25-11:21:08 INFO: 🔍 Debug: [WS => Shard 0] Waiting for event hello for 60000ms
08/07/25-11:21:09 INFO: 🔍 Debug: [WS => Shard 0] Preparing first heartbeat of the connection with a jitter of 0.7574638799197997; waiting 31245ms
08/07/25-11:21:09 INFO: 🔍 Debug: [WS => Shard 0] Waiting for identify throttle
08/07/25-11:21:09 INFO: 🔍 Debug: [WS => Shard 0] Identifying
	shard id: 0
	shard count: 1
	intents: 38449
	compression: none
08/07/25-11:21:09 INFO: 🔍 Debug: [WS => Shard 0] Waiting for event ready for 15000ms
08/07/25-11:21:09 INFO: ✅ Modern InterChat Client started successfully!
08/07/25-11:21:44 INFO: 🚀 Starting Modern InterChat Client (Cluster unknown)...
08/07/25-11:21:44 INFO: 🚀 Starting Modern InterChat Client...
08/07/25-11:21:44 INFO: 🔄 Loading command handlers dynamically...
08/07/25-11:21:44 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:21:44 INFO:   ✓ Loaded: donation-create (DonationCreateCommandHandler)
08/07/25-11:21:44 INFO:   ✓ Loaded: premium-status (PremiumStatusCommandHandler)
08/07/25-11:21:44 INFO:   ✓ Loaded: stats (StatsCommandHandler)
08/07/25-11:21:44 INFO:   ✓ Loaded: ping (PingCommandHandler)
08/07/25-11:21:44 INFO:   ✓ Loaded: about (AboutCommandHandler)
08/07/25-11:21:44 INFO: ✅ Loaded 5 command handlers
08/07/25-11:21:44 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-11:21:44 INFO: ✅ Cluster 0 container initialized
08/07/25-11:21:44 INFO: 📊 Environment: development
08/07/25-11:21:44 INFO: 🔧 Development mode: true
08/07/25-11:21:44 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:21:44 INFO:   ✓ Loaded: donation-create (DonationCreateCommandHandler)
08/07/25-11:21:44 INFO:   ✓ Loaded: premium-status (PremiumStatusCommandHandler)
08/07/25-11:21:44 INFO:   ✓ Loaded: stats (StatsCommandHandler)
08/07/25-11:21:44 INFO:   ✓ Loaded: ping (PingCommandHandler)
08/07/25-11:21:44 INFO:   ✓ Loaded: about (AboutCommandHandler)
08/07/25-11:21:44 INFO: ✅ Loaded 5 command handlers
08/07/25-11:21:44 INFO: ✅ Command system ready with 5 commands
08/07/25-11:21:44 INFO: 🔍 Debug: Provided token: Nzk4NzQ4MDE1NDM1MDU1MTM0.G0mkR8.**************************************
08/07/25-11:21:44 INFO: 🔍 Debug: Preparing to connect to the gateway...
08/07/25-11:21:45 INFO: 🌐 REST Debug: [REST Global(GET:/gateway/bot):global] Received bucket hash update
  Old Hash  : Global(GET:/gateway/bot)
  New Hash  : 41f9cd5d28af77da04563bcb1d67fdfd
08/07/25-11:21:45 INFO: 🔍 Debug: [WS => Manager] Fetched Gateway Information
	URL: wss://gateway.discord.gg
	Recommended Shards: 1
08/07/25-11:21:45 INFO: 🔍 Debug: [WS => Manager] Session Limit Information
	Total: 1000
	Remaining: 995
08/07/25-11:21:45 INFO: 🔍 Debug: [WS => Shard 0] Connecting to wss://gateway.discord.gg?v=10&encoding=json
08/07/25-11:21:45 INFO: 🔍 Debug: [WS => Shard 0] Waiting for event hello for 60000ms
08/07/25-11:21:45 INFO: 🔍 Debug: [WS => Shard 0] Preparing first heartbeat of the connection with a jitter of 0.4696977068757676; waiting 19375ms
08/07/25-11:21:45 INFO: 🔍 Debug: [WS => Shard 0] Waiting for identify throttle
08/07/25-11:21:45 INFO: 🔍 Debug: [WS => Shard 0] Identifying
	shard id: 0
	shard count: 1
	intents: 38449
	compression: none
08/07/25-11:21:45 INFO: 🔍 Debug: [WS => Shard 0] Waiting for event ready for 15000ms
08/07/25-11:21:45 INFO: ✅ Modern InterChat Client started successfully!
08/07/25-11:21:46 INFO: 🔍 Debug: [WS => Shard 0] Shard received all its guilds. Marking as fully ready.
08/07/25-11:21:46 INFO: ✅ Shard 0 ready
08/07/25-11:21:46 INFO: 🤖 Logged in as InterChat Beta#9206 (Cluster undefined)
08/07/25-11:21:46 INFO: 📊 Serving 13 guilds with 2 users
08/07/25-11:21:46 INFO: 🎉 Client ready event received for cluster 0
08/07/25-11:21:46 INFO: 📊 Cluster stats: 13 guilds, 2 users, shards: [0]
08/07/25-11:23:26 INFO: 🚀 Starting InterChat Client (Cluster unknown)...
08/07/25-11:23:26 INFO: 🚀 Starting Modern InterChat Client...
08/07/25-11:23:26 INFO: 🔄 Loading command handlers dynamically...
08/07/25-11:23:26 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:23:26 INFO: ✅ Loaded 5 command handlers
08/07/25-11:23:26 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-11:23:26 INFO: ✅ Cluster 0 container initialized
08/07/25-11:23:26 INFO: 📊 Environment: development
08/07/25-11:23:26 INFO: 🔧 Development mode: true
08/07/25-11:23:26 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:23:26 INFO: ✅ Loaded 5 command handlers
08/07/25-11:23:26 INFO: ✅ Command system ready with 5 commands
08/07/25-11:23:28 INFO: ✅ Modern InterChat Client started successfully!
08/07/25-11:23:44 INFO: 🚀 Starting InterChat Client (Cluster unknown)...
08/07/25-11:23:44 INFO: 🚀 Starting Modern InterChat Client...
08/07/25-11:23:44 INFO: 🔄 Loading command handlers dynamically...
08/07/25-11:23:44 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:23:44 INFO: ✅ Loaded 5 command handlers
08/07/25-11:23:44 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-11:23:45 INFO: ✅ Cluster 0 container initialized
08/07/25-11:23:45 INFO: 📊 Environment: development
08/07/25-11:23:45 INFO: 🔧 Development mode: true
08/07/25-11:23:45 INFO: 🔍 Loading command handlers dynamically...
08/07/25-11:23:45 INFO: ✅ Loaded 5 command handlers
08/07/25-11:23:45 INFO: ✅ Command system ready with 5 commands
08/07/25-11:23:46 INFO: ✅ Modern InterChat Client started successfully!
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-07:55:18 INFO: 🔄 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: 🔍 Loading command handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-07:55:18 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-07:55:18 INFO: ✅ Loaded 0 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:19:57 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:19:57 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:19:57 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:19:57 INFO: ✅ Loaded 6 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:46 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:46 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:46 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:46 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:47 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:47 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:47 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:47 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:47 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:47 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:47 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:47 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:47 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:47 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:47 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:47 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:47 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:47 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:47 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:47 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:47 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:47 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:47 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:47 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:47 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:47 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:47 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:47 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:34:47 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:34:47 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:34:47 INFO: ✅ Loaded 11 command handlers
08/07/25-09:34:47 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:34:47 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: [DonationCreatedNotificationHandler] Processing donation created event: {
  donationId: 'donation-123',
  donorId: 'user-456',
  amount: 25,
  currency: 'USD',
  tier: 1,
  premiumGranted: true,
  clusterId: 'test-cluster',
  occurredAt: 2025-07-08T09:38:48.650Z
}
08/07/25-09:38:48 INFO: ✅ Donation donation-123 created successfully for user user-456
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:38:48 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:38:48 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:38:48 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:38:48 INFO: ✅ Loaded 11 command handlers
08/07/25-09:52:24 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:52:24 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:52:24 INFO: ✅ Loaded 11 command handlers
08/07/25-09:52:24 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:52:24 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:52:24 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:52:24 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:52:24 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: ✅ Loaded 11 command handlers
08/07/25-09:52:25 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:52:25 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:52:25 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: ✅ Loaded 11 command handlers
08/07/25-09:52:25 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:52:25 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:52:25 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: ✅ Loaded 11 command handlers
08/07/25-09:52:25 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:52:25 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:52:25 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: ✅ Loaded 11 command handlers
08/07/25-09:52:25 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:52:25 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:52:25 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: ✅ Loaded 11 command handlers
08/07/25-09:52:25 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:52:25 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:52:25 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: ✅ Loaded 11 command handlers
08/07/25-09:52:25 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:52:25 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:52:25 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: ✅ Loaded 11 command handlers
08/07/25-09:52:25 INFO: 🔄 Loading interaction handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Discovered interaction directory: welcome
08/07/25-09:52:25 INFO: ✅ Interaction system initialized: 0 handlers from 1 directories
08/07/25-09:52:25 INFO: 🔄 Loading command handlers dynamically...
08/07/25-09:52:25 INFO: 🔍 Loading command handlers dynamically...
